"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/namecheap/route";
exports.ids = ["app/api/namecheap/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnamecheap%2Froute&page=%2Fapi%2Fnamecheap%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnamecheap%2Froute.ts&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnamecheap%2Froute&page=%2Fapi%2Fnamecheap%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnamecheap%2Froute.ts&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _home_dell_Desktop_wp_ai_app_src_app_api_namecheap_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/namecheap/route.ts */ \"(rsc)/./src/app/api/namecheap/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/namecheap/route\",\n        pathname: \"/api/namecheap\",\n        filename: \"route\",\n        bundlePath: \"app/api/namecheap/route\"\n    },\n    resolvedPagePath: \"/home/<USER>/Desktop/wp-ai-app/src/app/api/namecheap/route.ts\",\n    nextConfigOutput,\n    userland: _home_dell_Desktop_wp_ai_app_src_app_api_namecheap_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/namecheap/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnamecheap%2Froute&page=%2Fapi%2Fnamecheap%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnamecheap%2Froute.ts&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/namecheap/route.ts":
/*!****************************************!*\
  !*** ./src/app/api/namecheap/route.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n\n// Common TLDs with their approximate pricing (in USD, will be converted to AUD)\nconst COMMON_TLDS = [\n    {\n        tld: \"com\",\n        price: 12.98\n    },\n    {\n        tld: \"net\",\n        price: 14.98\n    },\n    {\n        tld: \"org\",\n        price: 14.98\n    },\n    {\n        tld: \"info\",\n        price: 18.98\n    },\n    {\n        tld: \"biz\",\n        price: 18.98\n    },\n    {\n        tld: \"com.au\",\n        price: 16.50\n    },\n    {\n        tld: \"net.au\",\n        price: 16.50\n    },\n    {\n        tld: \"org.au\",\n        price: 16.50\n    },\n    {\n        tld: \"co\",\n        price: 32.98\n    },\n    {\n        tld: \"io\",\n        price: 59.98\n    }\n];\n// USD to AUD conversion rate (approximate)\nconst USD_TO_AUD_RATE = 1.5;\nasync function checkDomainAvailability(domainList) {\n    const apiUser = process.env.NAMECHEAP_API_USER;\n    const apiKey = process.env.NAMECHEAP_API_KEY;\n    const username = process.env.NAMECHEAP_USERNAME;\n    const clientIp = process.env.NAMECHEAP_CLIENT_IP;\n    const sandbox = process.env.NAMECHEAP_SANDBOX === \"true\";\n    if (!apiUser || !apiKey || !username || !clientIp) {\n        throw new Error(\"Missing Namecheap API configuration\");\n    }\n    const baseUrl = sandbox ? \"https://api.sandbox.namecheap.com/xml.response\" : \"https://api.namecheap.com/xml.response\";\n    const params = new URLSearchParams({\n        ApiUser: apiUser,\n        ApiKey: apiKey,\n        UserName: username,\n        Command: \"namecheap.domains.check\",\n        ClientIp: clientIp,\n        DomainList: domainList.join(\",\")\n    });\n    try {\n        const response = await fetch(`${baseUrl}?${params.toString()}`, {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/x-www-form-urlencoded\"\n            }\n        });\n        if (!response.ok) {\n            throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        const xmlText = await response.text();\n        // Simple XML parsing for domain check results\n        const domainResults = [];\n        // Check for API errors first\n        const errorMatch = xmlText.match(/<Error[^>]*>([^<]*)<\\/Error>/);\n        if (errorMatch) {\n            throw new Error(`Namecheap API error: ${errorMatch[1]}`);\n        }\n        // Parse domain check results using regex\n        const domainRegex = /<DomainCheckResult[^>]*Domain=\"([^\"]*)\"[^>]*Available=\"([^\"]*)\"[^>]*IsPremiumName=\"([^\"]*)\"[^>]*(?:PremiumRegistrationPrice=\"([^\"]*)\")?[^>]*\\/>/g;\n        let match;\n        while((match = domainRegex.exec(xmlText)) !== null){\n            const [, domainName, available, isPremium, premiumPrice] = match;\n            const result = {\n                Domain: domainName,\n                Available: available === \"true\",\n                IsPremiumName: isPremium === \"true\"\n            };\n            if (result.IsPremiumName && premiumPrice) {\n                result.PremiumRegistrationPrice = parseFloat(premiumPrice);\n            }\n            domainResults.push(result);\n        }\n        return domainResults;\n    } catch (error) {\n        console.error(\"Namecheap API error:\", error);\n        throw error;\n    }\n}\nfunction generateDomainVariations(baseDomain) {\n    // Remove any existing TLD\n    const domainName = baseDomain.replace(/\\.(com|net|org|info|biz|com\\.au|net\\.au|org\\.au|co|io)$/i, \"\");\n    // Generate variations with common TLDs\n    return COMMON_TLDS.map(({ tld })=>`${domainName}.${tld}`);\n}\nfunction getPriceForDomain(domain, namecheapResult) {\n    // If it's a premium domain, use the premium price\n    if (namecheapResult.IsPremiumName && namecheapResult.PremiumRegistrationPrice) {\n        return namecheapResult.PremiumRegistrationPrice * USD_TO_AUD_RATE + 5; // Add 5 AUD markup\n    }\n    // Otherwise, use our standard pricing\n    const tld = domain.split(\".\").slice(1).join(\".\");\n    const tldInfo = COMMON_TLDS.find((t)=>t.tld === tld);\n    if (tldInfo) {\n        return tldInfo.price * USD_TO_AUD_RATE + 5; // Add 5 AUD markup\n    }\n    // Default price if TLD not found\n    return 20 * USD_TO_AUD_RATE + 5;\n}\nasync function POST(request) {\n    try {\n        const { domain, action } = await request.json();\n        if (!domain) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Domain is required\"\n            }, {\n                status: 400\n            });\n        }\n        if (action === \"check\") {\n            // Generate domain variations\n            const domainVariations = generateDomainVariations(domain);\n            // Check availability with Namecheap\n            const namecheapResults = await checkDomainAvailability(domainVariations);\n            // Format results for frontend\n            const results = namecheapResults.map((result)=>{\n                const formattedResult = {\n                    Domain: result.Domain,\n                    Available: result.Available,\n                    IsPremiumName: result.IsPremiumName\n                };\n                // Add price if available\n                if (result.Available) {\n                    formattedResult.Price = getPriceForDomain(result.Domain, result);\n                }\n                return formattedResult;\n            });\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                results\n            });\n        }\n        if (action === \"register\") {\n            // For now, return a mock success response\n            // In a real implementation, you would call the Namecheap domain registration API\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: true,\n                message: `Domain ${domain} registration initiated`,\n                orderId: `mock-order-${Date.now()}`\n            });\n        }\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Invalid action\"\n        }, {\n            status: 400\n        });\n    } catch (error) {\n        console.error(\"API error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: error instanceof Error ? error.message : \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/namecheap/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnamecheap%2Froute&page=%2Fapi%2Fnamecheap%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnamecheap%2Froute.ts&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();