/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/stripe/billing-data/route";
exports.ids = ["app/api/stripe/billing-data/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fstripe%2Fbilling-data%2Froute&page=%2Fapi%2Fstripe%2Fbilling-data%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstripe%2Fbilling-data%2Froute.ts&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fstripe%2Fbilling-data%2Froute&page=%2Fapi%2Fstripe%2Fbilling-data%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstripe%2Fbilling-data%2Froute.ts&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _home_dell_Desktop_wp_ai_app_src_app_api_stripe_billing_data_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/stripe/billing-data/route.ts */ \"(rsc)/./src/app/api/stripe/billing-data/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/stripe/billing-data/route\",\n        pathname: \"/api/stripe/billing-data\",\n        filename: \"route\",\n        bundlePath: \"app/api/stripe/billing-data/route\"\n    },\n    resolvedPagePath: \"/home/<USER>/Desktop/wp-ai-app/src/app/api/stripe/billing-data/route.ts\",\n    nextConfigOutput,\n    userland: _home_dell_Desktop_wp_ai_app_src_app_api_stripe_billing_data_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/stripe/billing-data/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fstripe%2Fbilling-data%2Froute&page=%2Fapi%2Fstripe%2Fbilling-data%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstripe%2Fbilling-data%2Froute.ts&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/stripe/billing-data/route.ts":
/*!**************************************************!*\
  !*** ./src/app/api/stripe/billing-data/route.ts ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var stripe__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! stripe */ \"(rsc)/./node_modules/stripe/esm/stripe.esm.node.js\");\n/* harmony import */ var _lib_auth_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth-utils */ \"(rsc)/./src/lib/auth-utils.ts\");\n\n\n\n// Get the authenticated user's Stripe Customer ID\nasync function getStripeCustomerId(req) {\n    try {\n        // Get authenticated user\n        const { user, error: authError } = await (0,_lib_auth_utils__WEBPACK_IMPORTED_MODULE_2__.getAuthenticatedUser)(req);\n        if (!user) {\n            return {\n                customerId: null,\n                error: authError || \"Authentication required\"\n            };\n        }\n        // Get user profile with Stripe customer ID\n        const { profile, error: profileError } = await (0,_lib_auth_utils__WEBPACK_IMPORTED_MODULE_2__.getUserProfile)(user.id);\n        if (!profile) {\n            return {\n                customerId: null,\n                error: profileError || \"User profile not found\"\n            };\n        }\n        return {\n            customerId: profile.stripe_customer_id,\n            error: null\n        };\n    } catch (error) {\n        return {\n            customerId: null,\n            error: error.message || \"Failed to get customer ID\"\n        };\n    }\n}\nconst stripe = new stripe__WEBPACK_IMPORTED_MODULE_1__[\"default\"](process.env.STRIPE_SECRET_KEY, {\n    apiVersion: \"2025-06-30.basil\"\n});\nasync function GET(req) {\n    try {\n        const { customerId, error } = await getStripeCustomerId(req);\n        if (!customerId) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: error || \"User not authenticated.\"\n            }, {\n                status: 401\n            });\n        }\n        // Fetch subscriptions, invoices, and payment methods from Stripe\n        const [subscriptions, invoices, paymentMethods] = await Promise.all([\n            stripe.subscriptions.list({\n                customer: customerId,\n                limit: 1\n            }),\n            stripe.invoices.list({\n                customer: customerId,\n                limit: 10\n            }),\n            stripe.paymentMethods.list({\n                customer: customerId,\n                type: \"card\"\n            })\n        ]);\n        // Format the data to send to the frontend\n        const responseData = {\n            subscription: subscriptions.data[0] ? {\n                status: subscriptions.data[0].status,\n                plan: subscriptions.data[0].items.data[0]?.price.nickname || \"N/A\",\n                amount: subscriptions.data[0].items.data[0]?.price.unit_amount / 100,\n                currency: subscriptions.data[0].items.data[0]?.price.currency,\n                nextBillingDate: new Date(subscriptions.data[0].current_period_end * 1000).toISOString()\n            } : null,\n            invoices: invoices.data.map((invoice)=>({\n                    id: invoice.id,\n                    date: new Date(invoice.created * 1000).toISOString(),\n                    amount: invoice.amount_paid / 100,\n                    status: invoice.status,\n                    url: invoice.invoice_pdf\n                })),\n            paymentMethods: paymentMethods.data.map((pm)=>({\n                    id: pm.id,\n                    type: pm.type,\n                    last4: pm.card?.last4,\n                    brand: pm.card?.brand,\n                    isDefault: false\n                }))\n        };\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(responseData);\n    } catch (error) {\n        console.error(\"Stripe API Error:\", error.message);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Internal Server Error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/stripe/billing-data/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth-utils.ts":
/*!*******************************!*\
  !*** ./src/lib/auth-utils.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createUserSite: () => (/* binding */ createUserSite),\n/* harmony export */   deleteUserSite: () => (/* binding */ deleteUserSite),\n/* harmony export */   getAuthenticatedUser: () => (/* binding */ getAuthenticatedUser),\n/* harmony export */   getTokenFromRequest: () => (/* binding */ getTokenFromRequest),\n/* harmony export */   getUserProfile: () => (/* binding */ getUserProfile),\n/* harmony export */   getUserSites: () => (/* binding */ getUserSites),\n/* harmony export */   requireAuth: () => (/* binding */ requireAuth),\n/* harmony export */   supabaseAdmin: () => (/* binding */ supabaseAdmin),\n/* harmony export */   updateUserSite: () => (/* binding */ updateUserSite),\n/* harmony export */   upsertUserProfile: () => (/* binding */ upsertUserProfile),\n/* harmony export */   validateAuthToken: () => (/* binding */ validateAuthToken)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n// Server-side Supabase client for API routes\nconst supabaseUrl = \"https://ermaaxnoyckezbjtegmq.supabase.co\";\nconst supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\nconst supabaseAdmin = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseServiceKey, {\n    auth: {\n        autoRefreshToken: false,\n        persistSession: false\n    }\n});\n// Extract JWT token from request headers\nconst getTokenFromRequest = (request)=>{\n    const authHeader = request.headers.get(\"authorization\");\n    if (authHeader && authHeader.startsWith(\"Bearer \")) {\n        return authHeader.substring(7);\n    }\n    return null;\n};\n// Validate JWT token and get user\nconst validateAuthToken = async (token)=>{\n    try {\n        const { data: { user }, error } = await supabaseAdmin.auth.getUser(token);\n        if (error || !user) {\n            return {\n                user: null,\n                error: error?.message || \"Invalid token\"\n            };\n        }\n        return {\n            user,\n            error: null\n        };\n    } catch (error) {\n        return {\n            user: null,\n            error: error.message || \"Token validation failed\"\n        };\n    }\n};\n// Get authenticated user from request\nconst getAuthenticatedUser = async (request)=>{\n    const token = getTokenFromRequest(request);\n    if (!token) {\n        return {\n            user: null,\n            error: \"No authorization token provided\"\n        };\n    }\n    return await validateAuthToken(token);\n};\n// Middleware helper to check if user is authenticated\nconst requireAuth = async (request)=>{\n    const { user, error } = await getAuthenticatedUser(request);\n    if (!user) {\n        return {\n            authenticated: false,\n            user: null,\n            error: error || \"Authentication required\"\n        };\n    }\n    return {\n        authenticated: true,\n        user,\n        error: null\n    };\n};\n// Get user profile with additional data\nconst getUserProfile = async (userId)=>{\n    try {\n        const { data: profile, error } = await supabaseAdmin.from(\"profiles\").select(\"*\").eq(\"id\", userId).single();\n        if (error) {\n            return {\n                profile: null,\n                error: error.message\n            };\n        }\n        return {\n            profile,\n            error: null\n        };\n    } catch (error) {\n        return {\n            profile: null,\n            error: error.message || \"Failed to fetch user profile\"\n        };\n    }\n};\n// Create or update user profile\nconst upsertUserProfile = async (userId, profileData)=>{\n    try {\n        const { data: profile, error } = await supabaseAdmin.from(\"profiles\").upsert({\n            id: userId,\n            ...profileData,\n            updated_at: new Date().toISOString()\n        }).select().single();\n        if (error) {\n            return {\n                profile: null,\n                error: error.message\n            };\n        }\n        return {\n            profile,\n            error: null\n        };\n    } catch (error) {\n        return {\n            profile: null,\n            error: error.message || \"Failed to update user profile\"\n        };\n    }\n};\n// Get user's sites\nconst getUserSites = async (userId)=>{\n    try {\n        const { data: sites, error } = await supabaseAdmin.from(\"sites\").select(\"*\").eq(\"user_id\", userId).order(\"created_at\", {\n            ascending: false\n        });\n        if (error) {\n            return {\n                sites: [],\n                error: error.message\n            };\n        }\n        return {\n            sites: sites || [],\n            error: null\n        };\n    } catch (error) {\n        return {\n            sites: [],\n            error: error.message || \"Failed to fetch user sites\"\n        };\n    }\n};\n// Create a new site for user\nconst createUserSite = async (userId, siteData)=>{\n    try {\n        const { data: site, error } = await supabaseAdmin.from(\"sites\").insert({\n            user_id: userId,\n            ...siteData\n        }).select().single();\n        if (error) {\n            return {\n                site: null,\n                error: error.message\n            };\n        }\n        return {\n            site,\n            error: null\n        };\n    } catch (error) {\n        return {\n            site: null,\n            error: error.message || \"Failed to create site\"\n        };\n    }\n};\n// Update user's site\nconst updateUserSite = async (userId, siteId, updates)=>{\n    try {\n        const { data: site, error } = await supabaseAdmin.from(\"sites\").update({\n            ...updates,\n            updated_at: new Date().toISOString()\n        }).eq(\"id\", siteId).eq(\"user_id\", userId) // Ensure user owns the site\n        .select().single();\n        if (error) {\n            return {\n                site: null,\n                error: error.message\n            };\n        }\n        return {\n            site,\n            error: null\n        };\n    } catch (error) {\n        return {\n            site: null,\n            error: error.message || \"Failed to update site\"\n        };\n    }\n};\n// Delete user's site\nconst deleteUserSite = async (userId, siteId)=>{\n    try {\n        const { error } = await supabaseAdmin.from(\"sites\").delete().eq(\"id\", siteId).eq(\"user_id\", userId) // Ensure user owns the site\n        ;\n        if (error) {\n            return {\n                success: false,\n                error: error.message\n            };\n        }\n        return {\n            success: true,\n            error: null\n        };\n    } catch (error) {\n        return {\n            success: false,\n            error: error.message || \"Failed to delete site\"\n        };\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth-utils.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/isows","vendor-chunks/stripe","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/qs","vendor-chunks/call-bind-apply-helpers","vendor-chunks/get-proto","vendor-chunks/object-inspect","vendor-chunks/has-symbols","vendor-chunks/gopd","vendor-chunks/function-bind","vendor-chunks/side-channel","vendor-chunks/side-channel-weakmap","vendor-chunks/side-channel-map","vendor-chunks/side-channel-list","vendor-chunks/hasown","vendor-chunks/get-intrinsic","vendor-chunks/es-object-atoms","vendor-chunks/es-define-property","vendor-chunks/dunder-proto","vendor-chunks/call-bound"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fstripe%2Fbilling-data%2Froute&page=%2Fapi%2Fstripe%2Fbilling-data%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstripe%2Fbilling-data%2Froute.ts&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();