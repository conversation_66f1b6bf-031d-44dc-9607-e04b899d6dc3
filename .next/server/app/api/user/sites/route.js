/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/user/sites/route";
exports.ids = ["app/api/user/sites/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fuser%2Fsites%2Froute&page=%2Fapi%2Fuser%2Fsites%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser%2Fsites%2Froute.ts&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fuser%2Fsites%2Froute&page=%2Fapi%2Fuser%2Fsites%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser%2Fsites%2Froute.ts&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _home_dell_Desktop_wp_ai_app_src_app_api_user_sites_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/user/sites/route.ts */ \"(rsc)/./src/app/api/user/sites/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/user/sites/route\",\n        pathname: \"/api/user/sites\",\n        filename: \"route\",\n        bundlePath: \"app/api/user/sites/route\"\n    },\n    resolvedPagePath: \"/home/<USER>/Desktop/wp-ai-app/src/app/api/user/sites/route.ts\",\n    nextConfigOutput,\n    userland: _home_dell_Desktop_wp_ai_app_src_app_api_user_sites_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/user/sites/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fuser%2Fsites%2Froute&page=%2Fapi%2Fuser%2Fsites%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser%2Fsites%2Froute.ts&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/user/sites/route.ts":
/*!*****************************************!*\
  !*** ./src/app/api/user/sites/route.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var _lib_api_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api-auth */ \"(rsc)/./src/lib/api-auth.ts\");\n/* harmony import */ var _lib_auth_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth-utils */ \"(rsc)/./src/lib/auth-utils.ts\");\n\n\n// GET /api/user/sites - Get all sites for the authenticated user\nconst GET = (0,_lib_api_auth__WEBPACK_IMPORTED_MODULE_0__.createAuthenticatedRoute)({\n    allowedMethods: [\n        \"GET\"\n    ],\n    handler: async (req, user)=>{\n        try {\n            const { sites, error } = await (0,_lib_auth_utils__WEBPACK_IMPORTED_MODULE_1__.getUserSites)(user.id);\n            if (error) {\n                return (0,_lib_api_auth__WEBPACK_IMPORTED_MODULE_0__.createErrorResponse)(error, 500);\n            }\n            return (0,_lib_api_auth__WEBPACK_IMPORTED_MODULE_0__.createSuccessResponse)({\n                sites\n            });\n        } catch (error) {\n            console.error(\"Error fetching user sites:\", error);\n            return (0,_lib_api_auth__WEBPACK_IMPORTED_MODULE_0__.createErrorResponse)(\"Failed to fetch sites\", 500);\n        }\n    }\n});\n// POST /api/user/sites - Create a new site for the authenticated user\nconst POST = (0,_lib_api_auth__WEBPACK_IMPORTED_MODULE_0__.createAuthenticatedRoute)({\n    allowedMethods: [\n        \"POST\"\n    ],\n    requiredFields: [\n        \"site_name\"\n    ],\n    handler: async (req, user)=>{\n        try {\n            const body = req.parsedBody;\n            const { site_name, expiry_status, expiry_time } = body;\n            const { site, error } = await (0,_lib_auth_utils__WEBPACK_IMPORTED_MODULE_1__.createUserSite)(user.id, {\n                site_name,\n                expiry_status: expiry_status || \"Temporary\",\n                expiry_time: expiry_time || null\n            });\n            if (error) {\n                return (0,_lib_api_auth__WEBPACK_IMPORTED_MODULE_0__.createErrorResponse)(error, 500);\n            }\n            return (0,_lib_api_auth__WEBPACK_IMPORTED_MODULE_0__.createSuccessResponse)({\n                site\n            }, 201);\n        } catch (error) {\n            console.error(\"Error creating user site:\", error);\n            return (0,_lib_api_auth__WEBPACK_IMPORTED_MODULE_0__.createErrorResponse)(\"Failed to create site\", 500);\n        }\n    }\n});\n// PUT /api/user/sites - Update a site for the authenticated user\nconst PUT = (0,_lib_api_auth__WEBPACK_IMPORTED_MODULE_0__.createAuthenticatedRoute)({\n    allowedMethods: [\n        \"PUT\"\n    ],\n    requiredFields: [\n        \"site_id\"\n    ],\n    handler: async (req, user)=>{\n        try {\n            const body = req.parsedBody;\n            const { site_id, site_name, expiry_status, expiry_time } = body;\n            const updates = {};\n            if (site_name !== undefined) updates.site_name = site_name;\n            if (expiry_status !== undefined) updates.expiry_status = expiry_status;\n            if (expiry_time !== undefined) updates.expiry_time = expiry_time;\n            const { site, error } = await (0,_lib_auth_utils__WEBPACK_IMPORTED_MODULE_1__.updateUserSite)(user.id, site_id, updates);\n            if (error) {\n                return (0,_lib_api_auth__WEBPACK_IMPORTED_MODULE_0__.createErrorResponse)(error, 500);\n            }\n            return (0,_lib_api_auth__WEBPACK_IMPORTED_MODULE_0__.createSuccessResponse)({\n                site\n            });\n        } catch (error) {\n            console.error(\"Error updating user site:\", error);\n            return (0,_lib_api_auth__WEBPACK_IMPORTED_MODULE_0__.createErrorResponse)(\"Failed to update site\", 500);\n        }\n    }\n});\n// DELETE /api/user/sites - Delete a site for the authenticated user\nconst DELETE = (0,_lib_api_auth__WEBPACK_IMPORTED_MODULE_0__.createAuthenticatedRoute)({\n    allowedMethods: [\n        \"DELETE\"\n    ],\n    requiredFields: [\n        \"site_id\"\n    ],\n    handler: async (req, user)=>{\n        try {\n            const body = req.parsedBody;\n            const { site_id } = body;\n            const { success, error } = await (0,_lib_auth_utils__WEBPACK_IMPORTED_MODULE_1__.deleteUserSite)(user.id, site_id);\n            if (error) {\n                return (0,_lib_api_auth__WEBPACK_IMPORTED_MODULE_0__.createErrorResponse)(error, 500);\n            }\n            return (0,_lib_api_auth__WEBPACK_IMPORTED_MODULE_0__.createSuccessResponse)({\n                success\n            });\n        } catch (error) {\n            console.error(\"Error deleting user site:\", error);\n            return (0,_lib_api_auth__WEBPACK_IMPORTED_MODULE_0__.createErrorResponse)(\"Failed to delete site\", 500);\n        }\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/user/sites/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/api-auth.ts":
/*!*****************************!*\
  !*** ./src/lib/api-auth.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createAuthenticatedRoute: () => (/* binding */ createAuthenticatedRoute),\n/* harmony export */   createErrorResponse: () => (/* binding */ createErrorResponse),\n/* harmony export */   createSuccessResponse: () => (/* binding */ createSuccessResponse),\n/* harmony export */   getAuthHeader: () => (/* binding */ getAuthHeader),\n/* harmony export */   parseJsonBody: () => (/* binding */ parseJsonBody),\n/* harmony export */   validateMethod: () => (/* binding */ validateMethod),\n/* harmony export */   validateRequiredFields: () => (/* binding */ validateRequiredFields),\n/* harmony export */   withAuth: () => (/* binding */ withAuth),\n/* harmony export */   withCors: () => (/* binding */ withCors)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _auth_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./auth-utils */ \"(rsc)/./src/lib/auth-utils.ts\");\n\n\n// Wrapper function to create authenticated API routes\nfunction withAuth(handler) {\n    return async (req, context)=>{\n        try {\n            // Get authenticated user\n            const { user, error } = await (0,_auth_utils__WEBPACK_IMPORTED_MODULE_1__.getAuthenticatedUser)(req);\n            if (!user) {\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    error: error || \"Authentication required\"\n                }, {\n                    status: 401\n                });\n            }\n            // Call the actual handler with the authenticated user\n            return await handler(req, user, context);\n        } catch (error) {\n            console.error(\"API authentication error:\", error);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Internal server error\"\n            }, {\n                status: 500\n            });\n        }\n    };\n}\n// Helper to get authorization header from request\nfunction getAuthHeader(req) {\n    return req.headers.get(\"authorization\");\n}\n// Helper to create error responses\nfunction createErrorResponse(message, status = 400) {\n    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n        error: message\n    }, {\n        status\n    });\n}\n// Helper to create success responses\nfunction createSuccessResponse(data, status = 200) {\n    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(data, {\n        status\n    });\n}\n// Middleware to add CORS headers\nfunction withCors(response) {\n    response.headers.set(\"Access-Control-Allow-Origin\", \"*\");\n    response.headers.set(\"Access-Control-Allow-Methods\", \"GET, POST, PUT, DELETE, OPTIONS\");\n    response.headers.set(\"Access-Control-Allow-Headers\", \"Content-Type, Authorization\");\n    return response;\n}\n// Helper to validate request method\nfunction validateMethod(req, allowedMethods) {\n    return allowedMethods.includes(req.method);\n}\n// Helper to parse JSON body safely\nasync function parseJsonBody(req) {\n    try {\n        const data = await req.json();\n        return {\n            data,\n            error: null\n        };\n    } catch (error) {\n        return {\n            data: null,\n            error: \"Invalid JSON body\"\n        };\n    }\n}\n// Helper to validate required fields\nfunction validateRequiredFields(data, requiredFields) {\n    for (const field of requiredFields){\n        if (!data[field]) {\n            return `Missing required field: ${field}`;\n        }\n    }\n    return null;\n}\n// Combined helper for authenticated API routes with common validations\nfunction createAuthenticatedRoute(options) {\n    return withAuth(async (req, user, context)=>{\n        // Validate method\n        if (!validateMethod(req, options.allowedMethods)) {\n            return createErrorResponse(`Method ${req.method} not allowed`, 405);\n        }\n        // Parse and validate body for non-GET requests\n        if (req.method !== \"GET\" && options.requiredFields) {\n            const { data, error } = await parseJsonBody(req);\n            if (error) {\n                return createErrorResponse(error, 400);\n            }\n            const validationError = validateRequiredFields(data, options.requiredFields);\n            if (validationError) {\n                return createErrorResponse(validationError, 400);\n            }\n            req.parsedBody = data;\n        }\n        return await options.handler(req, user, context);\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/api-auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth-utils.ts":
/*!*******************************!*\
  !*** ./src/lib/auth-utils.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createUserSite: () => (/* binding */ createUserSite),\n/* harmony export */   deleteUserSite: () => (/* binding */ deleteUserSite),\n/* harmony export */   getAuthenticatedUser: () => (/* binding */ getAuthenticatedUser),\n/* harmony export */   getTokenFromRequest: () => (/* binding */ getTokenFromRequest),\n/* harmony export */   getUserProfile: () => (/* binding */ getUserProfile),\n/* harmony export */   getUserSites: () => (/* binding */ getUserSites),\n/* harmony export */   requireAuth: () => (/* binding */ requireAuth),\n/* harmony export */   supabaseAdmin: () => (/* binding */ supabaseAdmin),\n/* harmony export */   updateUserSite: () => (/* binding */ updateUserSite),\n/* harmony export */   upsertUserProfile: () => (/* binding */ upsertUserProfile),\n/* harmony export */   validateAuthToken: () => (/* binding */ validateAuthToken)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n// Server-side Supabase client for API routes\nconst supabaseUrl = \"https://ermaaxnoyckezbjtegmq.supabase.co\";\nconst supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\nconst supabaseAdmin = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseServiceKey, {\n    auth: {\n        autoRefreshToken: false,\n        persistSession: false\n    }\n});\n// Extract JWT token from request headers\nconst getTokenFromRequest = (request)=>{\n    const authHeader = request.headers.get(\"authorization\");\n    if (authHeader && authHeader.startsWith(\"Bearer \")) {\n        return authHeader.substring(7);\n    }\n    return null;\n};\n// Validate JWT token and get user\nconst validateAuthToken = async (token)=>{\n    try {\n        const { data: { user }, error } = await supabaseAdmin.auth.getUser(token);\n        if (error || !user) {\n            return {\n                user: null,\n                error: error?.message || \"Invalid token\"\n            };\n        }\n        return {\n            user,\n            error: null\n        };\n    } catch (error) {\n        return {\n            user: null,\n            error: error.message || \"Token validation failed\"\n        };\n    }\n};\n// Get authenticated user from request\nconst getAuthenticatedUser = async (request)=>{\n    const token = getTokenFromRequest(request);\n    if (!token) {\n        return {\n            user: null,\n            error: \"No authorization token provided\"\n        };\n    }\n    return await validateAuthToken(token);\n};\n// Middleware helper to check if user is authenticated\nconst requireAuth = async (request)=>{\n    const { user, error } = await getAuthenticatedUser(request);\n    if (!user) {\n        return {\n            authenticated: false,\n            user: null,\n            error: error || \"Authentication required\"\n        };\n    }\n    return {\n        authenticated: true,\n        user,\n        error: null\n    };\n};\n// Get user profile with additional data\nconst getUserProfile = async (userId)=>{\n    try {\n        const { data: profile, error } = await supabaseAdmin.from(\"profiles\").select(\"*\").eq(\"id\", userId).single();\n        if (error) {\n            return {\n                profile: null,\n                error: error.message\n            };\n        }\n        return {\n            profile,\n            error: null\n        };\n    } catch (error) {\n        return {\n            profile: null,\n            error: error.message || \"Failed to fetch user profile\"\n        };\n    }\n};\n// Create or update user profile\nconst upsertUserProfile = async (userId, profileData)=>{\n    try {\n        const { data: profile, error } = await supabaseAdmin.from(\"profiles\").upsert({\n            id: userId,\n            ...profileData,\n            updated_at: new Date().toISOString()\n        }).select().single();\n        if (error) {\n            return {\n                profile: null,\n                error: error.message\n            };\n        }\n        return {\n            profile,\n            error: null\n        };\n    } catch (error) {\n        return {\n            profile: null,\n            error: error.message || \"Failed to update user profile\"\n        };\n    }\n};\n// Get user's sites\nconst getUserSites = async (userId)=>{\n    try {\n        const { data: sites, error } = await supabaseAdmin.from(\"sites\").select(\"*\").eq(\"user_id\", userId).order(\"created_at\", {\n            ascending: false\n        });\n        if (error) {\n            return {\n                sites: [],\n                error: error.message\n            };\n        }\n        return {\n            sites: sites || [],\n            error: null\n        };\n    } catch (error) {\n        return {\n            sites: [],\n            error: error.message || \"Failed to fetch user sites\"\n        };\n    }\n};\n// Create a new site for user\nconst createUserSite = async (userId, siteData)=>{\n    try {\n        const { data: site, error } = await supabaseAdmin.from(\"sites\").insert({\n            user_id: userId,\n            ...siteData\n        }).select().single();\n        if (error) {\n            return {\n                site: null,\n                error: error.message\n            };\n        }\n        return {\n            site,\n            error: null\n        };\n    } catch (error) {\n        return {\n            site: null,\n            error: error.message || \"Failed to create site\"\n        };\n    }\n};\n// Update user's site\nconst updateUserSite = async (userId, siteId, updates)=>{\n    try {\n        const { data: site, error } = await supabaseAdmin.from(\"sites\").update({\n            ...updates,\n            updated_at: new Date().toISOString()\n        }).eq(\"id\", siteId).eq(\"user_id\", userId) // Ensure user owns the site\n        .select().single();\n        if (error) {\n            return {\n                site: null,\n                error: error.message\n            };\n        }\n        return {\n            site,\n            error: null\n        };\n    } catch (error) {\n        return {\n            site: null,\n            error: error.message || \"Failed to update site\"\n        };\n    }\n};\n// Delete user's site\nconst deleteUserSite = async (userId, siteId)=>{\n    try {\n        const { error } = await supabaseAdmin.from(\"sites\").delete().eq(\"id\", siteId).eq(\"user_id\", userId) // Ensure user owns the site\n        ;\n        if (error) {\n            return {\n                success: false,\n                error: error.message\n            };\n        }\n        return {\n            success: true,\n            error: null\n        };\n    } catch (error) {\n        return {\n            success: false,\n            error: error.message || \"Failed to delete site\"\n        };\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth-utils.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/isows"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fuser%2Fsites%2Froute&page=%2Fapi%2Fuser%2Fsites%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser%2Fsites%2Froute.ts&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();