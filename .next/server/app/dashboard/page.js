/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/page";
exports.ids = ["app/dashboard/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(rsc)/./src/app/dashboard/page.tsx\")), \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/layout.tsx */ \"(rsc)/./src/app/dashboard/layout.tsx\")), \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/home/<USER>/Desktop/wp-ai-app/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/dashboard/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/page\",\n        pathname: \"/dashboard\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp%2Fdashboard%2Flayout.tsx&server=true!":
/*!****************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp%2Fdashboard%2Flayout.tsx&server=true! ***!
  \****************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/layout.tsx */ \"(ssr)/./src/app/dashboard/layout.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGaG9tZSUyRmRlbGwlMkZEZXNrdG9wJTJGd3AtYWktYXBwJTJGc3JjJTJGYXBwJTJGZGFzaGJvYXJkJTJGbGF5b3V0LnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly93b3JkcHJlc3MtYWktYXBwLz83Y2EzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL2hvbWUvZGVsbC9EZXNrdG9wL3dwLWFpLWFwcC9zcmMvYXBwL2Rhc2hib2FyZC9sYXlvdXQudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp%2Fdashboard%2Flayout.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp%2Fdashboard%2Fpage.tsx&server=true!":
/*!**************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp%2Fdashboard%2Fpage.tsx&server=true! ***!
  \**************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(ssr)/./src/app/dashboard/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGaG9tZSUyRmRlbGwlMkZEZXNrdG9wJTJGd3AtYWktYXBwJTJGc3JjJTJGYXBwJTJGZGFzaGJvYXJkJTJGcGFnZS50c3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vd29yZHByZXNzLWFpLWFwcC8/MjI4ZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9ob21lL2RlbGwvRGVza3RvcC93cC1haS1hcHAvc3JjL2FwcC9kYXNoYm9hcmQvcGFnZS50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp%2Fdashboard%2Fpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp%2Fstyles%2Fglobals.css&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fcomponents%2FErrorBoundary.tsx&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fcontext%2FAuthContext.tsx&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp%2Fstyles%2Fglobals.css&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fcomponents%2FErrorBoundary.tsx&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fcontext%2FAuthContext.tsx&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ErrorBoundary.tsx */ \"(ssr)/./src/components/ErrorBoundary.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/context/AuthContext.tsx */ \"(ssr)/./src/context/AuthContext.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGaG9tZSUyRmRlbGwlMkZEZXNrdG9wJTJGd3AtYWktYXBwJTJGc3JjJTJGYXBwJTJGc3R5bGVzJTJGZ2xvYmFscy5jc3MmbW9kdWxlcz0lMkZob21lJTJGZGVsbCUyRkRlc2t0b3AlMkZ3cC1haS1hcHAlMkZzcmMlMkZjb21wb25lbnRzJTJGRXJyb3JCb3VuZGFyeS50c3gmbW9kdWxlcz0lMkZob21lJTJGZGVsbCUyRkRlc2t0b3AlMkZ3cC1haS1hcHAlMkZzcmMlMkZjb250ZXh0JTJGQXV0aENvbnRleHQudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnTEFBa0c7QUFDbEciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly93b3JkcHJlc3MtYWktYXBwLz80MDMzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL2hvbWUvZGVsbC9EZXNrdG9wL3dwLWFpLWFwcC9zcmMvY29tcG9uZW50cy9FcnJvckJvdW5kYXJ5LnRzeFwiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL2hvbWUvZGVsbC9EZXNrdG9wL3dwLWFpLWFwcC9zcmMvY29udGV4dC9BdXRoQ29udGV4dC50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp%2Fstyles%2Fglobals.css&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fcomponents%2FErrorBoundary.tsx&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fcontext%2FAuthContext.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/dashboard/AskDomainModal.tsx":
/*!**********************************************!*\
  !*** ./src/app/dashboard/AskDomainModal.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst AskDomainModal = ({ isOpen, onYes, onNo })=>{\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-50 flex items-center justify-center bg-gray-600 bg-opacity-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative w-full max-w-md p-6 bg-white rounded-lg shadow-xl\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"mb-2 text-xl font-semibold text-gray-800 text-center\",\n                    children: \"Thank you for your purchase!\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/AskDomainModal.tsx\",\n                    lineNumber: 16,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mb-4 text-center text-gray-700\",\n                    children: \"Do you already have a custom domain you’d like to use?\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/AskDomainModal.tsx\",\n                    lineNumber: 19,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onYes,\n                            className: \"px-4 py-2 text-white bg-green-600 rounded-md hover:bg-green-700\",\n                            children: \"Yes\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/AskDomainModal.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onNo,\n                            className: \"px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300\",\n                            children: \"No\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/AskDomainModal.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/AskDomainModal.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/AskDomainModal.tsx\",\n            lineNumber: 15,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/AskDomainModal.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AskDomainModal);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/AskDomainModal.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/dashboard/MapDomainModal.tsx":
/*!**********************************************!*\
  !*** ./src/app/dashboard/MapDomainModal.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst MapDomainModal = ({ isOpen, onClose, siteName })=>{\n    const [domainName, setDomainName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [domainType, setDomainType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"Primary\");\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-50 flex items-center justify-center bg-gray-600 bg-opacity-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full max-w-md p-6 bg-white rounded-lg shadow-xl\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold\",\n                            children: \"Map Domain Name\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/MapDomainModal.tsx\",\n                            lineNumber: 19,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"text-gray-500 hover:text-gray-700\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                xmlns: \"http://www.w3.org/2000/svg\",\n                                className: \"w-6 h-6\",\n                                fill: \"none\",\n                                viewBox: \"0 0 24 24\",\n                                stroke: \"currentColor\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M6 18L18 6M6 6l12 12\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/MapDomainModal.tsx\",\n                                    lineNumber: 22,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/MapDomainModal.tsx\",\n                                lineNumber: 21,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/MapDomainModal.tsx\",\n                            lineNumber: 20,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/MapDomainModal.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            htmlFor: \"domainName\",\n                            className: \"block text-sm font-medium text-gray-700\",\n                            children: \"Enter Domain Name\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/MapDomainModal.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            id: \"domainName\",\n                            className: \"block w-full px-3 py-2 mt-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\",\n                            placeholder: \"example.com\",\n                            value: domainName,\n                            onChange: (e)=>setDomainName(e.target.value)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/MapDomainModal.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/MapDomainModal.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"inline-flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"radio\",\n                                        className: \"form-radio text-blue-600\",\n                                        name: \"domainType\",\n                                        value: \"Primary\",\n                                        checked: domainType === \"Primary\",\n                                        onChange: ()=>setDomainType(\"Primary\")\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/MapDomainModal.tsx\",\n                                        lineNumber: 42,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2\",\n                                        children: \"Primary\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/MapDomainModal.tsx\",\n                                        lineNumber: 50,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/MapDomainModal.tsx\",\n                                lineNumber: 41,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"inline-flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"radio\",\n                                        className: \"form-radio text-blue-600\",\n                                        name: \"domainType\",\n                                        value: \"Alias\",\n                                        checked: domainType === \"Alias\",\n                                        onChange: ()=>setDomainType(\"Alias\")\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/MapDomainModal.tsx\",\n                                        lineNumber: 53,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2\",\n                                        children: \"Alias\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/MapDomainModal.tsx\",\n                                        lineNumber: 61,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/MapDomainModal.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/MapDomainModal.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/MapDomainModal.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm font-medium text-gray-700\",\n                            children: \"Point 'CNAME' records to\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/MapDomainModal.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center p-3 mt-1 bg-gray-50 border border-gray-300 rounded-md\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"flex-1 text-sm text-gray-800 break-all\",\n                                    children: siteName\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/MapDomainModal.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"ml-2 text-gray-500 hover:text-gray-700\",\n                                    onClick: ()=>navigator.clipboard.writeText(siteName),\n                                    title: \"Copy to clipboard\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        className: \"w-5 h-5\",\n                                        fill: \"none\",\n                                        viewBox: \"0 0 24 24\",\n                                        stroke: \"currentColor\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M17 16l-4 4-4-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/MapDomainModal.tsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/MapDomainModal.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/MapDomainModal.tsx\",\n                                    lineNumber: 70,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/MapDomainModal.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/MapDomainModal.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-end space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300\",\n                            children: \"Cancel\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/MapDomainModal.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"px-4 py-2 text-white bg-green-500 rounded-md hover:bg-green-600\",\n                            onClick: ()=>{\n                                // Handle map domain logic here\n                                console.log(\"Mapping domain:\", domainName, \"as\", domainType);\n                                onClose();\n                            },\n                            children: \"Map Domain\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/MapDomainModal.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/MapDomainModal.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/MapDomainModal.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/MapDomainModal.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MapDomainModal);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/MapDomainModal.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/dashboard/layout.tsx":
/*!**************************************!*\
  !*** ./src/app/dashboard/layout.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Bell_ChevronDown_CreditCard_Globe_HelpCircle_Link_LogOut_PlusCircle_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,ChevronDown,CreditCard,Globe,HelpCircle,Link,LogOut,PlusCircle,Settings,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_ChevronDown_CreditCard_Globe_HelpCircle_Link_LogOut_PlusCircle_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,ChevronDown,CreditCard,Globe,HelpCircle,Link,LogOut,PlusCircle,Settings,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-plus.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_ChevronDown_CreditCard_Globe_HelpCircle_Link_LogOut_PlusCircle_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,ChevronDown,CreditCard,Globe,HelpCircle,Link,LogOut,PlusCircle,Settings,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/link.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_ChevronDown_CreditCard_Globe_HelpCircle_Link_LogOut_PlusCircle_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,ChevronDown,CreditCard,Globe,HelpCircle,Link,LogOut,PlusCircle,Settings,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_ChevronDown_CreditCard_Globe_HelpCircle_Link_LogOut_PlusCircle_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,ChevronDown,CreditCard,Globe,HelpCircle,Link,LogOut,PlusCircle,Settings,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_ChevronDown_CreditCard_Globe_HelpCircle_Link_LogOut_PlusCircle_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,ChevronDown,CreditCard,Globe,HelpCircle,Link,LogOut,PlusCircle,Settings,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_ChevronDown_CreditCard_Globe_HelpCircle_Link_LogOut_PlusCircle_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,ChevronDown,CreditCard,Globe,HelpCircle,Link,LogOut,PlusCircle,Settings,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_ChevronDown_CreditCard_Globe_HelpCircle_Link_LogOut_PlusCircle_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,ChevronDown,CreditCard,Globe,HelpCircle,Link,LogOut,PlusCircle,Settings,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-help.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_ChevronDown_CreditCard_Globe_HelpCircle_Link_LogOut_PlusCircle_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,ChevronDown,CreditCard,Globe,HelpCircle,Link,LogOut,PlusCircle,Settings,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_ChevronDown_CreditCard_Globe_HelpCircle_Link_LogOut_PlusCircle_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,ChevronDown,CreditCard,Globe,HelpCircle,Link,LogOut,PlusCircle,Settings,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/context/AuthContext */ \"(ssr)/./src/context/AuthContext.tsx\");\n/* harmony import */ var _hooks_useAuthGuard__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useAuthGuard */ \"(ssr)/./src/hooks/useAuthGuard.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction DashboardLayout({ children }) {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [showUserMenu, setShowUserMenu] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Ensure user is authenticated\n    const { user } = (0,_hooks_useAuthGuard__WEBPACK_IMPORTED_MODULE_5__.useRequireAuth)();\n    const { signOut } = (0,_context_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const handleSignOut = async ()=>{\n        await signOut();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-[#eaf3e1]\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                className: \"w-64 bg-[#3d5c3a] text-white flex flex-col justify-between py-6 px-0 fixed inset-y-0 left-0 z-30\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center px-8 mb-10\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-[#eaf3e1] rounded-lg w-10 h-10 flex items-center justify-center mr-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-2xl font-bold text-[#3d5c3a]\",\n                                        children: \"W\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                        lineNumber: 28,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                    lineNumber: 27,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-2xl font-bold tracking-tight\",\n                                    children: \"AI builder\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                    lineNumber: 30,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                            lineNumber: 26,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            href: \"/dashboard\",\n                                            className: `flex items-center px-8 py-3 rounded-l-full transition-colors ${ false ? 0 : \"hover:bg-[#4e6e4a] hover:text-white\"}`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_ChevronDown_CreditCard_Globe_HelpCircle_Link_LogOut_PlusCircle_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                    lineNumber: 36,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-4\",\n                                                    children: \"Websites\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                    lineNumber: 37,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                            lineNumber: 35,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                        lineNumber: 34,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            href: \"/dashboard/create\",\n                                            className: `flex items-center px-8 py-3 rounded-l-full transition-colors ${ false ? 0 : \"hover:bg-[#4e6e4a] hover:text-white\"}`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_ChevronDown_CreditCard_Globe_HelpCircle_Link_LogOut_PlusCircle_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                    lineNumber: 42,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-4\",\n                                                    children: \"Create\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                    lineNumber: 43,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                            lineNumber: 41,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                        lineNumber: 40,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            href: \"/dashboard/domain\",\n                                            className: `flex items-center px-8 py-3 rounded-l-full transition-colors ${ false ? 0 : \"hover:bg-[#4e6e4a] hover:text-white\"}`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_ChevronDown_CreditCard_Globe_HelpCircle_Link_LogOut_PlusCircle_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                    lineNumber: 48,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-4\",\n                                                    children: \"Domain\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                    lineNumber: 49,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                            lineNumber: 47,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                        lineNumber: 46,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            href: \"/dashboard/account\",\n                                            className: `flex items-center px-8 py-3 rounded-l-full transition-colors ${ false ? 0 : \"hover:bg-[#4e6e4a] hover:text-white\"}`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_ChevronDown_CreditCard_Globe_HelpCircle_Link_LogOut_PlusCircle_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                    lineNumber: 54,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-4\",\n                                                    children: \"Account\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                    lineNumber: 55,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                            lineNumber: 53,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                        lineNumber: 52,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            href: \"/dashboard/billing\",\n                                            className: `flex items-center px-8 py-3 rounded-l-full transition-colors ${ false ? 0 : \"hover:bg-[#4e6e4a] hover:text-white\"}`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_ChevronDown_CreditCard_Globe_HelpCircle_Link_LogOut_PlusCircle_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                    lineNumber: 60,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-4\",\n                                                    children: \"Billing\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                    lineNumber: 61,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            href: \"/dashboard/settings\",\n                                            className: `flex items-center px-8 py-3 rounded-l-full transition-colors ${ false ? 0 : \"hover:bg-[#4e6e4a] hover:text-white\"}`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_ChevronDown_CreditCard_Globe_HelpCircle_Link_LogOut_PlusCircle_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                    lineNumber: 66,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-4\",\n                                                    children: \"Settings\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                    lineNumber: 67,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                            lineNumber: 65,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                        lineNumber: 64,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                            lineNumber: 32,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col flex-1 min-h-screen ml-64 overflow-x-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"sticky top-0 z-10 bg-white border-b border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between h-16 p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-lg font-semibold text-gray-800\",\n                                        children: [\n                                            \"Welcome back, \",\n                                            user?.email?.split(\"@\")[0] || \"User\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"p-2 rounded-full hover:bg-gray-100\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_ChevronDown_CreditCard_Globe_HelpCircle_Link_LogOut_PlusCircle_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"w-6 h-6 text-gray-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                lineNumber: 87,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                            lineNumber: 86,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"p-2 rounded-full hover:bg-gray-100\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_ChevronDown_CreditCard_Globe_HelpCircle_Link_LogOut_PlusCircle_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"w-6 h-6 text-gray-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                lineNumber: 90,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setShowUserMenu(!showUserMenu),\n                                                    className: \"flex items-center space-x-2 p-2 rounded-full hover:bg-gray-100\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_ChevronDown_CreditCard_Globe_HelpCircle_Link_LogOut_PlusCircle_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"w-6 h-6 text-gray-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                            lineNumber: 99,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_ChevronDown_CreditCard_Globe_HelpCircle_Link_LogOut_PlusCircle_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"w-4 h-4 text-gray-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                            lineNumber: 100,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                    lineNumber: 95,\n                                                    columnNumber: 17\n                                                }, this),\n                                                showUserMenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-50\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"py-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"px-4 py-2 text-sm text-gray-700 border-b border-gray-100\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-medium\",\n                                                                        children: user?.email\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                                        lineNumber: 107,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-gray-500\",\n                                                                        children: \"Signed in\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                                        lineNumber: 108,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                                lineNumber: 106,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                                href: \"/dashboard/account\",\n                                                                className: \"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                                                                onClick: ()=>setShowUserMenu(false),\n                                                                children: \"Account Settings\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                                lineNumber: 110,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                                href: \"/dashboard/billing\",\n                                                                className: \"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                                                                onClick: ()=>setShowUserMenu(false),\n                                                                children: \"Billing\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                                lineNumber: 117,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleSignOut,\n                                                                className: \"w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100 flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_ChevronDown_CreditCard_Globe_HelpCircle_Link_LogOut_PlusCircle_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                        className: \"w-4 h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                                        lineNumber: 128,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Sign Out\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                                        lineNumber: 129,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                                lineNumber: 124,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                        lineNumber: 105,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                    lineNumber: 104,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 p-6 overflow-y-auto\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                lineNumber: 76,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2Rhc2hib2FyZC9sYXlvdXQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQzZCO0FBQ1c7QUFDK0c7QUFDM0c7QUFDSTtBQUNNO0FBRXZDLFNBQVNnQixnQkFBZ0IsRUFBRUMsUUFBUSxFQUFpQztJQUNqRixNQUFNQyxTQUFTTCwwREFBU0E7SUFDeEIsTUFBTSxDQUFDTSxjQUFjQyxnQkFBZ0IsR0FBR2xCLCtDQUFRQSxDQUFDO0lBRWpELCtCQUErQjtJQUMvQixNQUFNLEVBQUVtQixJQUFJLEVBQUUsR0FBR04sbUVBQWNBO0lBQy9CLE1BQU0sRUFBRU8sT0FBTyxFQUFFLEdBQUdSLDZEQUFPQTtJQUUzQixNQUFNUyxnQkFBZ0I7UUFDcEIsTUFBTUQ7SUFDUjtJQUVBLHFCQUNFLDhEQUFDRTtRQUFJQyxXQUFVOzswQkFFYiw4REFBQ0M7Z0JBQU1ELFdBQVU7MEJBQ2YsNEVBQUNEOztzQ0FDQyw4REFBQ0E7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDRDtvQ0FBSUMsV0FBVTs4Q0FDYiw0RUFBQ0U7d0NBQUtGLFdBQVU7a0RBQW9DOzs7Ozs7Ozs7Ozs4Q0FFdEQsOERBQUNFO29DQUFLRixXQUFVOzhDQUFvQzs7Ozs7Ozs7Ozs7O3NDQUV0RCw4REFBQ0c7c0NBQ0MsNEVBQUNDO2dDQUFHSixXQUFVOztrREFDWiw4REFBQ0s7a0RBQ0MsNEVBQUM5QixpREFBSUE7NENBQUMrQixNQUFLOzRDQUFhTixXQUFXLENBQUMsNkRBQTZELEVBQUUsTUFBOEQsR0FBd0IsSUFBaUQsc0NBQXNDLENBQUM7OzhEQUMvUSw4REFBQ25CLDZKQUFLQTtvREFBQ21CLFdBQVU7Ozs7Ozs4REFDakIsOERBQUNFO29EQUFLRixXQUFVOzhEQUFPOzs7Ozs7Ozs7Ozs7Ozs7OztrREFHM0IsOERBQUNLO2tEQUNDLDRFQUFDOUIsaURBQUlBOzRDQUFDK0IsTUFBSzs0Q0FBb0JOLFdBQVcsQ0FBQyw2REFBNkQsRUFBRSxNQUE4RCxHQUFzQixJQUFpRCxzQ0FBc0MsQ0FBQzs7OERBQ3BSLDhEQUFDbEIsNkpBQVVBO29EQUFDa0IsV0FBVTs7Ozs7OzhEQUN0Qiw4REFBQ0U7b0RBQUtGLFdBQVU7OERBQU87Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQUczQiw4REFBQ0s7a0RBQ0MsNEVBQUM5QixpREFBSUE7NENBQUMrQixNQUFLOzRDQUFvQk4sV0FBVyxDQUFDLDZEQUE2RCxFQUFFLE1BQThELEdBQXNCLElBQWlELHNDQUFzQyxDQUFDOzs4REFDcFIsOERBQUNmLDZKQUFRQTtvREFBQ2UsV0FBVTs7Ozs7OzhEQUNwQiw4REFBQ0U7b0RBQUtGLFdBQVU7OERBQU87Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQUczQiw4REFBQ0s7a0RBQ0MsNEVBQUM5QixpREFBSUE7NENBQUMrQixNQUFLOzRDQUFxQk4sV0FBVyxDQUFDLDZEQUE2RCxFQUFFLE1BQThELEdBQXVCLElBQWlELHNDQUFzQyxDQUFDOzs4REFDdFIsOERBQUNwQiw2SkFBSUE7b0RBQUNvQixXQUFVOzs7Ozs7OERBQ2hCLDhEQUFDRTtvREFBS0YsV0FBVTs4REFBTzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBRzNCLDhEQUFDSztrREFDQyw0RUFBQzlCLGlEQUFJQTs0Q0FBQytCLE1BQUs7NENBQXFCTixXQUFXLENBQUMsNkRBQTZELEVBQUUsTUFBOEQsR0FBdUIsSUFBaUQsc0NBQXNDLENBQUM7OzhEQUN0Uiw4REFBQ2hCLDhKQUFVQTtvREFBQ2dCLFdBQVU7Ozs7Ozs4REFDdEIsOERBQUNFO29EQUFLRixXQUFVOzhEQUFPOzs7Ozs7Ozs7Ozs7Ozs7OztrREFHM0IsOERBQUNLO2tEQUNDLDRFQUFDOUIsaURBQUlBOzRDQUFDK0IsTUFBSzs0Q0FBc0JOLFdBQVcsQ0FBQyw2REFBNkQsRUFBRSxNQUE4RCxHQUF3QixJQUFpRCxzQ0FBc0MsQ0FBQzs7OERBQ3hSLDhEQUFDakIsOEpBQVFBO29EQUFDaUIsV0FBVTs7Ozs7OzhEQUNwQiw4REFBQ0U7b0RBQUtGLFdBQVU7OERBQU87Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFTbkMsOERBQUNEO2dCQUFJQyxXQUFVOztrQ0FFYiw4REFBQ1U7d0JBQU9WLFdBQVU7a0NBQ2hCLDRFQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNEO29DQUFJQyxXQUFVOzhDQUNiLDRFQUFDVzt3Q0FBR1gsV0FBVTs7NENBQXNDOzRDQUNuQ0osTUFBTWdCLE9BQU9DLE1BQU0sSUFBSSxDQUFDLEVBQUUsSUFBSTs7Ozs7Ozs7Ozs7OzhDQUdqRCw4REFBQ2Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDYzs0Q0FBT2QsV0FBVTtzREFDaEIsNEVBQUN0Qiw4SkFBSUE7Z0RBQUNzQixXQUFVOzs7Ozs7Ozs7OztzREFFbEIsOERBQUNjOzRDQUFPZCxXQUFVO3NEQUNoQiw0RUFBQ3JCLDhKQUFVQTtnREFBQ3FCLFdBQVU7Ozs7Ozs7Ozs7O3NEQUl4Qiw4REFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDYztvREFDQ0MsU0FBUyxJQUFNcEIsZ0JBQWdCLENBQUNEO29EQUNoQ00sV0FBVTs7c0VBRVYsOERBQUNwQiw2SkFBSUE7NERBQUNvQixXQUFVOzs7Ozs7c0VBQ2hCLDhEQUFDYiw4SkFBV0E7NERBQUNhLFdBQVU7Ozs7Ozs7Ozs7OztnREFHeEJOLDhCQUNDLDhEQUFDSztvREFBSUMsV0FBVTs4REFDYiw0RUFBQ0Q7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDRDtnRUFBSUMsV0FBVTs7a0ZBQ2IsOERBQUNEO3dFQUFJQyxXQUFVO2tGQUFlSixNQUFNZ0I7Ozs7OztrRkFDcEMsOERBQUNiO3dFQUFJQyxXQUFVO2tGQUF3Qjs7Ozs7Ozs7Ozs7OzBFQUV6Qyw4REFBQ3pCLGlEQUFJQTtnRUFDSCtCLE1BQUs7Z0VBQ0xOLFdBQVU7Z0VBQ1ZlLFNBQVMsSUFBTXBCLGdCQUFnQjswRUFDaEM7Ozs7OzswRUFHRCw4REFBQ3BCLGlEQUFJQTtnRUFDSCtCLE1BQUs7Z0VBQ0xOLFdBQVU7Z0VBQ1ZlLFNBQVMsSUFBTXBCLGdCQUFnQjswRUFDaEM7Ozs7OzswRUFHRCw4REFBQ21CO2dFQUNDQyxTQUFTakI7Z0VBQ1RFLFdBQVU7O2tGQUVWLDhEQUFDZCw4SkFBTUE7d0VBQUNjLFdBQVU7Ozs7OztrRkFDbEIsOERBQUNFO2tGQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQVd0Qiw4REFBQ2M7d0JBQUtoQixXQUFVO2tDQUNiUjs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBS1giLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly93b3JkcHJlc3MtYWktYXBwLy4vc3JjL2FwcC9kYXNoYm9hcmQvbGF5b3V0LnRzeD83MjNjIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiXG5pbXBvcnQgTGluayBmcm9tICduZXh0L2xpbmsnO1xuaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgQmVsbCwgSGVscENpcmNsZSwgVXNlciwgR2xvYmUsIFBsdXNDaXJjbGUsIFNldHRpbmdzLCBMYXlvdXREYXNoYm9hcmQsIENyZWRpdENhcmQsIExpbmsgYXMgTGlua0ljb24sIExvZ091dCwgQ2hldnJvbkRvd24gfSBmcm9tICdsdWNpZGUtcmVhY3QnO1xuaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJztcbmltcG9ydCB7IHVzZUF1dGggfSBmcm9tICdAL2NvbnRleHQvQXV0aENvbnRleHQnO1xuaW1wb3J0IHsgdXNlUmVxdWlyZUF1dGggfSBmcm9tICdAL2hvb2tzL3VzZUF1dGhHdWFyZCc7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIERhc2hib2FyZExheW91dCh7IGNoaWxkcmVuIH06IHsgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZSB9KSB7XG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpO1xuICBjb25zdCBbc2hvd1VzZXJNZW51LCBzZXRTaG93VXNlck1lbnVdID0gdXNlU3RhdGUoZmFsc2UpO1xuXG4gIC8vIEVuc3VyZSB1c2VyIGlzIGF1dGhlbnRpY2F0ZWRcbiAgY29uc3QgeyB1c2VyIH0gPSB1c2VSZXF1aXJlQXV0aCgpO1xuICBjb25zdCB7IHNpZ25PdXQgfSA9IHVzZUF1dGgoKTtcblxuICBjb25zdCBoYW5kbGVTaWduT3V0ID0gYXN5bmMgKCkgPT4ge1xuICAgIGF3YWl0IHNpZ25PdXQoKTtcbiAgfTtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBoLXNjcmVlbiBiZy1bI2VhZjNlMV1cIj5cbiAgICAgIHsvKiBTaWRlYmFyICovfVxuICAgICAgPGFzaWRlIGNsYXNzTmFtZT1cInctNjQgYmctWyMzZDVjM2FdIHRleHQtd2hpdGUgZmxleCBmbGV4LWNvbCBqdXN0aWZ5LWJldHdlZW4gcHktNiBweC0wIGZpeGVkIGluc2V0LXktMCBsZWZ0LTAgei0zMFwiPlxuICAgICAgICA8ZGl2PlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgcHgtOCBtYi0xMFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1bI2VhZjNlMV0gcm91bmRlZC1sZyB3LTEwIGgtMTAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgbXItM1wiPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1bIzNkNWMzYV1cIj5XPC9zcGFuPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdHJhY2tpbmctdGlnaHRcIj5BSSBidWlsZGVyPC9zcGFuPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDxuYXY+XG4gICAgICAgICAgICA8dWwgY2xhc3NOYW1lPVwic3BhY2UteS0xXCI+XG4gICAgICAgICAgICAgIDxsaT5cbiAgICAgICAgICAgICAgICA8TGluayBocmVmPVwiL2Rhc2hib2FyZFwiIGNsYXNzTmFtZT17YGZsZXggaXRlbXMtY2VudGVyIHB4LTggcHktMyByb3VuZGVkLWwtZnVsbCB0cmFuc2l0aW9uLWNvbG9ycyAke3R5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnICYmIHdpbmRvdy5sb2NhdGlvbi5wYXRobmFtZSA9PT0gJy9kYXNoYm9hcmQvd2Vic2l0ZXMnID8gJ2JnLXdoaXRlIHRleHQtWyMzZDVjM2FdIGZvbnQtc2VtaWJvbGQgc2hhZG93JyA6ICdob3ZlcjpiZy1bIzRlNmU0YV0gaG92ZXI6dGV4dC13aGl0ZSd9YH0+IFxuICAgICAgICAgICAgICAgICAgPEdsb2JlIGNsYXNzTmFtZT1cInctNSBoLTVcIiAvPlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwibWwtNFwiPldlYnNpdGVzPC9zcGFuPlxuICAgICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgPC9saT5cbiAgICAgICAgICAgICAgPGxpPlxuICAgICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvZGFzaGJvYXJkL2NyZWF0ZVwiIGNsYXNzTmFtZT17YGZsZXggaXRlbXMtY2VudGVyIHB4LTggcHktMyByb3VuZGVkLWwtZnVsbCB0cmFuc2l0aW9uLWNvbG9ycyAke3R5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnICYmIHdpbmRvdy5sb2NhdGlvbi5wYXRobmFtZSA9PT0gJy9kYXNoYm9hcmQvY3JlYXRlJyA/ICdiZy13aGl0ZSB0ZXh0LVsjM2Q1YzNhXSBmb250LXNlbWlib2xkIHNoYWRvdycgOiAnaG92ZXI6YmctWyM0ZTZlNGFdIGhvdmVyOnRleHQtd2hpdGUnfWB9PiBcbiAgICAgICAgICAgICAgICAgIDxQbHVzQ2lyY2xlIGNsYXNzTmFtZT1cInctNSBoLTVcIiAvPlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwibWwtNFwiPkNyZWF0ZTwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICAgIDwvbGk+XG4gICAgICAgICAgICAgIDxsaT5cbiAgICAgICAgICAgICAgICA8TGluayBocmVmPVwiL2Rhc2hib2FyZC9kb21haW5cIiBjbGFzc05hbWU9e2BmbGV4IGl0ZW1zLWNlbnRlciBweC04IHB5LTMgcm91bmRlZC1sLWZ1bGwgdHJhbnNpdGlvbi1jb2xvcnMgJHt0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJyAmJiB3aW5kb3cubG9jYXRpb24ucGF0aG5hbWUgPT09ICcvZGFzaGJvYXJkL2RvbWFpbicgPyAnYmctd2hpdGUgdGV4dC1bIzNkNWMzYV0gZm9udC1zZW1pYm9sZCBzaGFkb3cnIDogJ2hvdmVyOmJnLVsjNGU2ZTRhXSBob3Zlcjp0ZXh0LXdoaXRlJ31gfT5cbiAgICAgICAgICAgICAgICAgIDxMaW5rSWNvbiBjbGFzc05hbWU9XCJ3LTUgaC01XCIgLz5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm1sLTRcIj5Eb21haW48L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICA8L2xpPlxuICAgICAgICAgICAgICA8bGk+XG4gICAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9kYXNoYm9hcmQvYWNjb3VudFwiIGNsYXNzTmFtZT17YGZsZXggaXRlbXMtY2VudGVyIHB4LTggcHktMyByb3VuZGVkLWwtZnVsbCB0cmFuc2l0aW9uLWNvbG9ycyAke3R5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnICYmIHdpbmRvdy5sb2NhdGlvbi5wYXRobmFtZSA9PT0gJy9kYXNoYm9hcmQvYWNjb3VudCcgPyAnYmctd2hpdGUgdGV4dC1bIzNkNWMzYV0gZm9udC1zZW1pYm9sZCBzaGFkb3cnIDogJ2hvdmVyOmJnLVsjNGU2ZTRhXSBob3Zlcjp0ZXh0LXdoaXRlJ31gfT4gXG4gICAgICAgICAgICAgICAgICA8VXNlciBjbGFzc05hbWU9XCJ3LTUgaC01XCIgLz5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm1sLTRcIj5BY2NvdW50PC9zcGFuPlxuICAgICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgPC9saT5cbiAgICAgICAgICAgICAgPGxpPlxuICAgICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvZGFzaGJvYXJkL2JpbGxpbmdcIiBjbGFzc05hbWU9e2BmbGV4IGl0ZW1zLWNlbnRlciBweC04IHB5LTMgcm91bmRlZC1sLWZ1bGwgdHJhbnNpdGlvbi1jb2xvcnMgJHt0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJyAmJiB3aW5kb3cubG9jYXRpb24ucGF0aG5hbWUgPT09ICcvZGFzaGJvYXJkL2JpbGxpbmcnID8gJ2JnLXdoaXRlIHRleHQtWyMzZDVjM2FdIGZvbnQtc2VtaWJvbGQgc2hhZG93JyA6ICdob3ZlcjpiZy1bIzRlNmU0YV0gaG92ZXI6dGV4dC13aGl0ZSd9YH0+XG4gICAgICAgICAgICAgICAgICA8Q3JlZGl0Q2FyZCBjbGFzc05hbWU9XCJ3LTUgaC01XCIgLz5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm1sLTRcIj5CaWxsaW5nPC9zcGFuPlxuICAgICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgPC9saT5cbiAgICAgICAgICAgICAgPGxpPlxuICAgICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvZGFzaGJvYXJkL3NldHRpbmdzXCIgY2xhc3NOYW1lPXtgZmxleCBpdGVtcy1jZW50ZXIgcHgtOCBweS0zIHJvdW5kZWQtbC1mdWxsIHRyYW5zaXRpb24tY29sb3JzICR7dHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcgJiYgd2luZG93LmxvY2F0aW9uLnBhdGhuYW1lID09PSAnL2Rhc2hib2FyZC9zZXR0aW5ncycgPyAnYmctd2hpdGUgdGV4dC1bIzNkNWMzYV0gZm9udC1zZW1pYm9sZCBzaGFkb3cnIDogJ2hvdmVyOmJnLVsjNGU2ZTRhXSBob3Zlcjp0ZXh0LXdoaXRlJ31gfT4gXG4gICAgICAgICAgICAgICAgICA8U2V0dGluZ3MgY2xhc3NOYW1lPVwidy01IGgtNVwiIC8+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJtbC00XCI+U2V0dGluZ3M8L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICA8L2xpPlxuICAgICAgICAgICAgPC91bD5cbiAgICAgICAgICA8L25hdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2FzaWRlPlxuXG4gICAgICB7LyogTWFpbiBDb250ZW50ICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIGZsZXgtMSBtaW4taC1zY3JlZW4gbWwtNjQgb3ZlcmZsb3cteC1hdXRvXCI+XG4gICAgICAgIHsvKiBIZWFkZXIgKi99XG4gICAgICAgIDxoZWFkZXIgY2xhc3NOYW1lPVwic3RpY2t5IHRvcC0wIHotMTAgYmctd2hpdGUgYm9yZGVyLWIgYm9yZGVyLWdyYXktMjAwXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gaC0xNiBwLTRcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktODAwXCI+XG4gICAgICAgICAgICAgICAgV2VsY29tZSBiYWNrLCB7dXNlcj8uZW1haWw/LnNwbGl0KCdAJylbMF0gfHwgJ1VzZXInfVxuICAgICAgICAgICAgICA8L2gyPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNFwiPlxuICAgICAgICAgICAgICA8YnV0dG9uIGNsYXNzTmFtZT1cInAtMiByb3VuZGVkLWZ1bGwgaG92ZXI6YmctZ3JheS0xMDBcIj5cbiAgICAgICAgICAgICAgICA8QmVsbCBjbGFzc05hbWU9XCJ3LTYgaC02IHRleHQtZ3JheS02MDBcIiAvPlxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9XCJwLTIgcm91bmRlZC1mdWxsIGhvdmVyOmJnLWdyYXktMTAwXCI+XG4gICAgICAgICAgICAgICAgPEhlbHBDaXJjbGUgY2xhc3NOYW1lPVwidy02IGgtNiB0ZXh0LWdyYXktNjAwXCIgLz5cbiAgICAgICAgICAgICAgPC9idXR0b24+XG5cbiAgICAgICAgICAgICAgey8qIFVzZXIgTWVudSAqL31cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxuICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dVc2VyTWVudSghc2hvd1VzZXJNZW51KX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMiBwLTIgcm91bmRlZC1mdWxsIGhvdmVyOmJnLWdyYXktMTAwXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8VXNlciBjbGFzc05hbWU9XCJ3LTYgaC02IHRleHQtZ3JheS02MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgPENoZXZyb25Eb3duIGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC1ncmF5LTYwMFwiIC8+XG4gICAgICAgICAgICAgICAgPC9idXR0b24+XG5cbiAgICAgICAgICAgICAgICB7c2hvd1VzZXJNZW51ICYmIChcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgcmlnaHQtMCBtdC0yIHctNDggYmctd2hpdGUgcm91bmRlZC1tZCBzaGFkb3ctbGcgYm9yZGVyIGJvcmRlci1ncmF5LTIwMCB6LTUwXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHktMVwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHgtNCBweS0yIHRleHQtc20gdGV4dC1ncmF5LTcwMCBib3JkZXItYiBib3JkZXItZ3JheS0xMDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj57dXNlcj8uZW1haWx9PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMFwiPlNpZ25lZCBpbjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxMaW5rXG4gICAgICAgICAgICAgICAgICAgICAgICBocmVmPVwiL2Rhc2hib2FyZC9hY2NvdW50XCJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJsb2NrIHB4LTQgcHktMiB0ZXh0LXNtIHRleHQtZ3JheS03MDAgaG92ZXI6YmctZ3JheS0xMDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd1VzZXJNZW51KGZhbHNlKX1cbiAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICBBY2NvdW50IFNldHRpbmdzXG4gICAgICAgICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICAgICAgICAgIDxMaW5rXG4gICAgICAgICAgICAgICAgICAgICAgICBocmVmPVwiL2Rhc2hib2FyZC9iaWxsaW5nXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJsb2NrIHB4LTQgcHktMiB0ZXh0LXNtIHRleHQtZ3JheS03MDAgaG92ZXI6YmctZ3JheS0xMDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd1VzZXJNZW51KGZhbHNlKX1cbiAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICBCaWxsaW5nXG4gICAgICAgICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZVNpZ25PdXR9XG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgdGV4dC1sZWZ0IHB4LTQgcHktMiB0ZXh0LXNtIHRleHQtcmVkLTYwMCBob3ZlcjpiZy1ncmF5LTEwMCBmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIlxuICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxMb2dPdXQgY2xhc3NOYW1lPVwidy00IGgtNFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj5TaWduIE91dDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2hlYWRlcj5cblxuICAgICAgICB7LyogUGFnZSBDb250ZW50ICovfVxuICAgICAgICA8bWFpbiBjbGFzc05hbWU9XCJmbGV4LTEgcC02IG92ZXJmbG93LXktYXV0b1wiPlxuICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgPC9tYWluPlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gICk7XG59Il0sIm5hbWVzIjpbIkxpbmsiLCJSZWFjdCIsInVzZVN0YXRlIiwiQmVsbCIsIkhlbHBDaXJjbGUiLCJVc2VyIiwiR2xvYmUiLCJQbHVzQ2lyY2xlIiwiU2V0dGluZ3MiLCJDcmVkaXRDYXJkIiwiTGlua0ljb24iLCJMb2dPdXQiLCJDaGV2cm9uRG93biIsInVzZVJvdXRlciIsInVzZUF1dGgiLCJ1c2VSZXF1aXJlQXV0aCIsIkRhc2hib2FyZExheW91dCIsImNoaWxkcmVuIiwicm91dGVyIiwic2hvd1VzZXJNZW51Iiwic2V0U2hvd1VzZXJNZW51IiwidXNlciIsInNpZ25PdXQiLCJoYW5kbGVTaWduT3V0IiwiZGl2IiwiY2xhc3NOYW1lIiwiYXNpZGUiLCJzcGFuIiwibmF2IiwidWwiLCJsaSIsImhyZWYiLCJ3aW5kb3ciLCJsb2NhdGlvbiIsInBhdGhuYW1lIiwiaGVhZGVyIiwiaDIiLCJlbWFpbCIsInNwbGl0IiwiYnV0dG9uIiwib25DbGljayIsIm1haW4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useAuthGuard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useAuthGuard */ \"(ssr)/./src/hooks/useAuthGuard.ts\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase */ \"(ssr)/./src/lib/supabase.ts\");\n/* harmony import */ var _MapDomainModal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./MapDomainModal */ \"(ssr)/./src/app/dashboard/MapDomainModal.tsx\");\n/* harmony import */ var _AskDomainModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./AskDomainModal */ \"(ssr)/./src/app/dashboard/AskDomainModal.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nconst DashboardPage = ()=>{\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useSearchParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    const [sites, setSites] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [search, setSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isMapDomainOpen, setIsMapDomainOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAskDomainOpen, setIsAskDomainOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedSiteName, setSelectedSiteName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedSiteId, setSelectedSiteId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Ensure user is authenticated\n    const { user } = (0,_hooks_useAuthGuard__WEBPACK_IMPORTED_MODULE_2__.useRequireAuth)();\n    const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_3__.createSupabaseClient)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Trigger AskDomainModal if redirected from checkout\n        const postCheckout = searchParams.get(\"postCheckout\");\n        const siteIdFromParam = searchParams.get(\"siteId\");\n        if (postCheckout && siteIdFromParam) {\n            setSelectedSiteId(siteIdFromParam);\n            setIsAskDomainOpen(true);\n        }\n    }, [\n        searchParams\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchSites = async ()=>{\n            if (!user) {\n                setLoading(false);\n                return;\n            }\n            try {\n                // Get the session to include in API requests\n                const { data: { session } } = await supabase.auth.getSession();\n                if (!session) {\n                    throw new Error(\"No active session\");\n                }\n                // Fetch sites using the authenticated API\n                const response = await fetch(\"/api/user/sites\", {\n                    headers: {\n                        \"Authorization\": `Bearer ${session.access_token}`,\n                        \"Content-Type\": \"application/json\"\n                    }\n                });\n                if (!response.ok) {\n                    throw new Error(\"Failed to fetch sites\");\n                }\n                const { sites } = await response.json();\n                setSites(sites);\n            } catch (err) {\n                console.error(\"Error fetching sites:\", err);\n                setError(err instanceof Error ? err.message : \"An unknown error occurred\");\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchSites();\n    }, [\n        user,\n        supabase\n    ]);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"py-8 text-center\",\n            children: \"Loading sites...\"\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n            lineNumber: 84,\n            columnNumber: 12\n        }, undefined);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"py-8 text-center text-red-500\",\n            children: [\n                \"Error: \",\n                error\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n            lineNumber: 88,\n            columnNumber: 12\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full p-8 bg-gray-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between p-6 mb-6 bg-white rounded-lg shadow-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-semibold text-gray-800\",\n                        children: \"Websites\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    placeholder: \"Search\",\n                                    className: \"py-2 pl-10 pr-4 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                    value: search,\n                                    onChange: (e)=>setSearch(e.target.value)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"absolute w-5 h-5 text-gray-400 transform -translate-y-1/2 left-3 top-1/2\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: \"2\",\n                                        d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                lineNumber: 94,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-hidden bg-white rounded-lg shadow-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                    className: \"min-w-full divide-y divide-gray-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                            className: \"bg-gray-50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        scope: \"col\",\n                                        className: \"px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase\",\n                                        children: \"Site Name\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        scope: \"col\",\n                                        className: \"px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase\",\n                                        children: \"Actions\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        scope: \"col\",\n                                        className: \"px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase\",\n                                        children: \"Expiry\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                            className: \"bg-white divide-y divide-gray-200\",\n                            children: (search.trim() ? sites.filter((site)=>site.site_name.toLowerCase().includes(search.toLowerCase())) : sites).map((site)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"flex items-center px-6 py-4 text-sm font-medium text-gray-900 whitespace-nowrap\",\n                                            children: [\n                                                site.site_name,\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"ml-2 text-gray-400 hover:text-gray-600\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        className: \"w-4 h-4\",\n                                                        fill: \"none\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        stroke: \"currentColor\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 155,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 154,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4 text-sm font-medium text-right whitespace-nowrap\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"text-blue-500 hover:text-blue-700\",\n                                                        title: \"Map Domain\",\n                                                        onClick: ()=>{\n                                                            setSelectedSiteName(site.site_name);\n                                                            setIsMapDomainOpen(true);\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            className: \"w-5 h-5\",\n                                                            fill: \"none\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            stroke: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 168,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 167,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 162,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    site.expiry_status === \"Temporary\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"text-green-500 hover:text-green-700\",\n                                                        title: \"Choose Plan\",\n                                                        onClick: ()=>{\n                                                            window.location.href = `/payments?siteId=${site.id}`;\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            className: \"w-5 h-5\",\n                                                            fill: \"none\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            stroke: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M13 10V3L4 14h7v7l9-11h-7z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 179,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 178,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 173,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    site.expiry_status === \"Permanent\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"text-purple-500 hover:text-purple-700\",\n                                                        title: \"Change Plan\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            className: \"w-5 h-5\",\n                                                            fill: \"none\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            stroke: \"currentColor\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                                                                    x: \"2\",\n                                                                    y: \"7\",\n                                                                    width: \"20\",\n                                                                    height: \"10\",\n                                                                    rx: \"2\",\n                                                                    ry: \"2\",\n                                                                    stroke: \"currentColor\",\n                                                                    strokeWidth: \"2\",\n                                                                    fill: \"none\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                                    lineNumber: 190,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M2 11h20\",\n                                                                    stroke: \"currentColor\",\n                                                                    strokeWidth: \"2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                                    lineNumber: 191,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                    cx: \"7\",\n                                                                    cy: \"15\",\n                                                                    r: \"1\",\n                                                                    fill: \"currentColor\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                                    lineNumber: 192,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                    cx: \"11\",\n                                                                    cy: \"15\",\n                                                                    r: \"1\",\n                                                                    fill: \"currentColor\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                                    lineNumber: 193,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 189,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 185,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4 text-sm whitespace-nowrap\",\n                                            children: site.expiry_status === \"Temporary\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: `px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800`,\n                                                title: (()=>{\n                                                    if (!site.expiry_time) return \"No expiry time set\";\n                                                    const expiry = new Date(site.expiry_time);\n                                                    const now = new Date();\n                                                    const diffMs = expiry.getTime() - now.getTime();\n                                                    if (diffMs <= 0) return \"Expired\";\n                                                    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));\n                                                    const diffHours = Math.floor(diffMs % (1000 * 60 * 60 * 24) / (1000 * 60 * 60));\n                                                    return `${diffDays} days ${diffHours} hours left (expires at ${expiry.toLocaleString()})`;\n                                                })(),\n                                                children: site.expiry_status\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 21\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: `px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800`,\n                                                children: site.expiry_status\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                            lineNumber: 199,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, site.id, true, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                    lineNumber: 125,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 text-sm text-gray-600 bg-white border-t border-gray-200 rounded-b-lg\",\n                children: [\n                    sites.length,\n                    \" Sites\"\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                lineNumber: 231,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MapDomainModal__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                isOpen: isMapDomainOpen,\n                onClose: ()=>setIsMapDomainOpen(false),\n                siteName: selectedSiteName\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                lineNumber: 234,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AskDomainModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                isOpen: isAskDomainOpen,\n                onYes: ()=>{\n                    // open map domain modal\n                    const site = sites.find((s)=>s.id === selectedSiteId);\n                    if (site) {\n                        setSelectedSiteName(site.site_name);\n                        setIsMapDomainOpen(true);\n                    }\n                    setIsAskDomainOpen(false);\n                },\n                onNo: ()=>{\n                    router.push(`/dashboard/domain`);\n                    setIsAskDomainOpen(false);\n                }\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                lineNumber: 240,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n        lineNumber: 92,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DashboardPage);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ErrorBoundary.tsx":
/*!******************************************!*\
  !*** ./src/components/ErrorBoundary.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorBoundary: () => (/* binding */ ErrorBoundary),\n/* harmony export */   ErrorDisplay: () => (/* binding */ ErrorDisplay),\n/* harmony export */   Loading: () => (/* binding */ Loading),\n/* harmony export */   SuccessDisplay: () => (/* binding */ SuccessDisplay)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Home,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Home,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Home,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* __next_internal_client_entry_do_not_use__ ErrorBoundary,ErrorDisplay,SuccessDisplay,Loading auto */ \n\n\nclass ErrorBoundary extends react__WEBPACK_IMPORTED_MODULE_1__.Component {\n    constructor(props){\n        super(props);\n        this.state = {\n            hasError: false\n        };\n    }\n    static getDerivedStateFromError(error) {\n        return {\n            hasError: true,\n            error\n        };\n    }\n    componentDidCatch(error, errorInfo) {\n        console.error(\"ErrorBoundary caught an error:\", error, errorInfo);\n    }\n    render() {\n        if (this.state.hasError) {\n            if (this.props.fallback) {\n                return this.props.fallback;\n            }\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"sm:mx-auto sm:w-full sm:max-w-md\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    className: \"mx-auto h-12 w-12 text-red-500\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/ErrorBoundary.tsx\",\n                                    lineNumber: 41,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"mt-4 text-lg font-medium text-gray-900\",\n                                    children: \"Something went wrong\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/ErrorBoundary.tsx\",\n                                    lineNumber: 42,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-2 text-sm text-gray-600\",\n                                    children: \"We encountered an unexpected error. Please try refreshing the page.\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/ErrorBoundary.tsx\",\n                                    lineNumber: 45,\n                                    columnNumber: 17\n                                }, this),\n                                 true && this.state.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                                    className: \"mt-4 text-left\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                            className: \"cursor-pointer text-sm text-gray-500\",\n                                            children: \"Error details (development only)\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/ErrorBoundary.tsx\",\n                                            lineNumber: 50,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                            className: \"mt-2 text-xs text-red-600 bg-red-50 p-2 rounded overflow-auto\",\n                                            children: this.state.error.stack\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/ErrorBoundary.tsx\",\n                                            lineNumber: 53,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/ErrorBoundary.tsx\",\n                                    lineNumber: 49,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6 flex flex-col sm:flex-row gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>window.location.reload(),\n                                            className: \"flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/ErrorBoundary.tsx\",\n                                                    lineNumber: 63,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Refresh Page\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/ErrorBoundary.tsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>window.location.href = \"/\",\n                                            className: \"flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/ErrorBoundary.tsx\",\n                                                    lineNumber: 70,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Go Home\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/ErrorBoundary.tsx\",\n                                            lineNumber: 66,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/ErrorBoundary.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/ErrorBoundary.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/ErrorBoundary.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/ErrorBoundary.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/ErrorBoundary.tsx\",\n                lineNumber: 37,\n                columnNumber: 9\n            }, this);\n        }\n        return this.props.children;\n    }\n}\nfunction ErrorDisplay({ error, className = \"\", onDismiss }) {\n    if (!error) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `bg-red-50 border border-red-200 rounded-md p-4 ${className}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        className: \"h-5 w-5 text-red-400\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/ErrorBoundary.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/ErrorBoundary.tsx\",\n                    lineNumber: 98,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"ml-3 flex-1\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-red-800\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/ErrorBoundary.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/ErrorBoundary.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 9\n                }, this),\n                onDismiss && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"ml-auto pl-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"-mx-1.5 -my-1.5\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onDismiss,\n                            className: \"inline-flex bg-red-50 rounded-md p-1.5 text-red-500 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-red-50 focus:ring-red-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sr-only\",\n                                    children: \"Dismiss\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/ErrorBoundary.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"h-3 w-3\",\n                                    viewBox: \"0 0 20 20\",\n                                    fill: \"currentColor\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fillRule: \"evenodd\",\n                                        d: \"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\",\n                                        clipRule: \"evenodd\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/ErrorBoundary.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/ErrorBoundary.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/ErrorBoundary.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/ErrorBoundary.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/ErrorBoundary.tsx\",\n                    lineNumber: 105,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/ErrorBoundary.tsx\",\n            lineNumber: 97,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/ErrorBoundary.tsx\",\n        lineNumber: 96,\n        columnNumber: 5\n    }, this);\n}\nfunction SuccessDisplay({ message, className = \"\", onDismiss }) {\n    if (!message) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `bg-green-50 border border-green-200 rounded-md p-4 ${className}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"h-5 w-5 text-green-400\",\n                        viewBox: \"0 0 20 20\",\n                        fill: \"currentColor\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            fillRule: \"evenodd\",\n                            d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                            clipRule: \"evenodd\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/ErrorBoundary.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/ErrorBoundary.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/ErrorBoundary.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"ml-3 flex-1\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-green-800\",\n                        children: message\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/ErrorBoundary.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/ErrorBoundary.tsx\",\n                    lineNumber: 142,\n                    columnNumber: 9\n                }, this),\n                onDismiss && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"ml-auto pl-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"-mx-1.5 -my-1.5\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onDismiss,\n                            className: \"inline-flex bg-green-50 rounded-md p-1.5 text-green-500 hover:bg-green-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-green-50 focus:ring-green-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sr-only\",\n                                    children: \"Dismiss\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/ErrorBoundary.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"h-3 w-3\",\n                                    viewBox: \"0 0 20 20\",\n                                    fill: \"currentColor\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fillRule: \"evenodd\",\n                                        d: \"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\",\n                                        clipRule: \"evenodd\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/ErrorBoundary.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/ErrorBoundary.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/ErrorBoundary.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/ErrorBoundary.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/ErrorBoundary.tsx\",\n                    lineNumber: 146,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/ErrorBoundary.tsx\",\n            lineNumber: 136,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/ErrorBoundary.tsx\",\n        lineNumber: 135,\n        columnNumber: 5\n    }, this);\n}\nfunction Loading({ message = \"Loading...\", className = \"\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `flex items-center justify-center p-8 ${className}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/ErrorBoundary.tsx\",\n                    lineNumber: 175,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mt-2 text-sm text-gray-600\",\n                    children: message\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/ErrorBoundary.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/ErrorBoundary.tsx\",\n            lineNumber: 174,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/ErrorBoundary.tsx\",\n        lineNumber: 173,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ErrorBoundary.tsx\n");

/***/ }),

/***/ "(ssr)/./src/context/AuthContext.tsx":
/*!*************************************!*\
  !*** ./src/context/AuthContext.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase */ \"(ssr)/./src/lib/supabase.ts\");\n/* harmony import */ var _hooks_useErrorHandler__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useErrorHandler */ \"(ssr)/./src/hooks/useErrorHandler.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\n\n\n// Create the auth context with default values\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    user: null,\n    session: null,\n    isLoading: true,\n    error: null,\n    signIn: async ()=>({\n            error: null\n        }),\n    signUp: async ()=>({\n            error: null\n        }),\n    signOut: async ()=>{},\n    resetPassword: async ()=>({\n            error: null\n        }),\n    updateProfile: async ()=>({\n            error: null\n        })\n});\n// Create the auth provider component\nconst AuthProvider = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_3__.createSupabaseClient)();\n    const { handleAuthError, setErrorMessage, clearError, error } = (0,_hooks_useErrorHandler__WEBPACK_IMPORTED_MODULE_4__.useErrorHandler)();\n    // Initialize auth state\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeAuth = async ()=>{\n            try {\n                // Get the initial session\n                const { data: { session: initialSession } } = await supabase.auth.getSession();\n                setSession(initialSession);\n                setUser(initialSession?.user ?? null);\n                // Set up auth state change listener\n                const { data: { subscription } } = await supabase.auth.onAuthStateChange((_event, newSession)=>{\n                    setSession(newSession);\n                    setUser(newSession?.user ?? null);\n                    setIsLoading(false);\n                });\n                // Cleanup subscription on unmount\n                return ()=>{\n                    subscription.unsubscribe();\n                };\n            } catch (err) {\n                console.error(\"Error initializing auth:\", err);\n                setErrorMessage(\"Failed to initialize authentication\");\n                setIsLoading(false);\n            }\n        };\n        initializeAuth();\n    }, [\n        supabase\n    ]);\n    // Sign in with email and password\n    const signIn = async (email, password)=>{\n        try {\n            clearError();\n            const { error } = await supabase.auth.signInWithPassword({\n                email,\n                password\n            });\n            if (error) {\n                handleAuthError(error);\n                return {\n                    error: error.message\n                };\n            }\n            return {\n                error: null\n            };\n        } catch (err) {\n            handleAuthError(err);\n            return {\n                error: err.message || \"An error occurred during sign in\"\n            };\n        }\n    };\n    // Sign up with email and password\n    const signUp = async (email, password)=>{\n        try {\n            clearError();\n            const { error } = await supabase.auth.signUp({\n                email,\n                password\n            });\n            if (error) {\n                handleAuthError(error);\n                return {\n                    error: error.message\n                };\n            }\n            return {\n                error: null\n            };\n        } catch (err) {\n            handleAuthError(err);\n            return {\n                error: err.message || \"An error occurred during sign up\"\n            };\n        }\n    };\n    // Sign out\n    const signOut = async ()=>{\n        try {\n            await supabase.auth.signOut();\n            router.push(\"/login\");\n        } catch (err) {\n            console.error(\"Error signing out:\", err);\n            setError(err.message || \"An error occurred during sign out\");\n        }\n    };\n    // Reset password\n    const resetPassword = async (email)=>{\n        try {\n            setError(null);\n            const { error } = await supabase.auth.resetPasswordForEmail(email, {\n                redirectTo: `${window.location.origin}/reset-password`\n            });\n            if (error) {\n                setError(error.message);\n                return {\n                    error: error.message\n                };\n            }\n            return {\n                error: null\n            };\n        } catch (err) {\n            const errorMessage = err.message || \"An error occurred during password reset\";\n            setError(errorMessage);\n            return {\n                error: errorMessage\n            };\n        }\n    };\n    // Update user profile\n    const updateProfile = async (data)=>{\n        try {\n            setError(null);\n            const { error } = await supabase.auth.updateUser({\n                data\n            });\n            if (error) {\n                setError(error.message);\n                return {\n                    error: error.message\n                };\n            }\n            return {\n                error: null\n            };\n        } catch (err) {\n            const errorMessage = err.message || \"An error occurred while updating profile\";\n            setError(errorMessage);\n            return {\n                error: errorMessage\n            };\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            user,\n            session,\n            isLoading,\n            error,\n            signIn,\n            signUp,\n            signOut,\n            resetPassword,\n            updateProfile\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/context/AuthContext.tsx\",\n        lineNumber: 165,\n        columnNumber: 5\n    }, undefined);\n};\n// Custom hook to use the auth context\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/context/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useAuthGuard.ts":
/*!***********************************!*\
  !*** ./src/hooks/useAuthGuard.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuthGuard: () => (/* binding */ useAuthGuard),\n/* harmony export */   useRedirectIfAuthenticated: () => (/* binding */ useRedirectIfAuthenticated),\n/* harmony export */   useRequireAuth: () => (/* binding */ useRequireAuth),\n/* harmony export */   useRouteProtection: () => (/* binding */ useRouteProtection)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/context/AuthContext */ \"(ssr)/./src/context/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ useAuthGuard,useRequireAuth,useRedirectIfAuthenticated,useRouteProtection auto */ \n\n\n// Define public routes that don't require authentication\nconst PUBLIC_ROUTES = [\n    \"/\",\n    \"/login\",\n    \"/signup\",\n    \"/reset-password\",\n    \"/verify-email\"\n];\n// Define routes that should redirect authenticated users (like login/signup)\nconst AUTH_REDIRECT_ROUTES = [\n    \"/login\",\n    \"/signup\"\n];\nconst useAuthGuard = (options = {})=>{\n    const { redirectTo = \"/login\", requireAuth = true, redirectIfAuthenticated = false } = options;\n    const { user, isLoading } = (0,_context_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname)();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        // Don't do anything while loading\n        if (isLoading) return;\n        // If user is authenticated and should be redirected (e.g., on login page)\n        if (user && redirectIfAuthenticated) {\n            router.push(\"/dashboard\");\n            return;\n        }\n        // If authentication is required and user is not authenticated\n        if (requireAuth && !user) {\n            // Check if current route is public\n            const isPublicRoute = PUBLIC_ROUTES.some((route)=>pathname === route || pathname.startsWith(route + \"/\"));\n            if (!isPublicRoute) {\n                router.push(redirectTo);\n                return;\n            }\n        }\n    }, [\n        user,\n        isLoading,\n        pathname,\n        router,\n        redirectTo,\n        requireAuth,\n        redirectIfAuthenticated\n    ]);\n    return {\n        user,\n        isLoading,\n        isAuthenticated: !!user,\n        isPublicRoute: PUBLIC_ROUTES.includes(pathname),\n        shouldRedirect: requireAuth && !user && !PUBLIC_ROUTES.includes(pathname)\n    };\n};\n// Hook specifically for protected routes\nconst useRequireAuth = (redirectTo = \"/login\")=>{\n    return useAuthGuard({\n        requireAuth: true,\n        redirectTo\n    });\n};\n// Hook specifically for auth pages (login/signup) that should redirect if already authenticated\nconst useRedirectIfAuthenticated = (redirectTo = \"/dashboard\")=>{\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname)();\n    const shouldRedirect = AUTH_REDIRECT_ROUTES.includes(pathname);\n    return useAuthGuard({\n        requireAuth: false,\n        redirectIfAuthenticated: shouldRedirect,\n        redirectTo\n    });\n};\n// Hook to check if a route should be protected\nconst useRouteProtection = ()=>{\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname)();\n    const isPublicRoute = PUBLIC_ROUTES.some((route)=>pathname === route || pathname.startsWith(route + \"/\"));\n    const isAuthRoute = AUTH_REDIRECT_ROUTES.includes(pathname);\n    const isDashboardRoute = pathname.startsWith(\"/dashboard\");\n    const isApiRoute = pathname.startsWith(\"/api\");\n    return {\n        isPublicRoute,\n        isAuthRoute,\n        isDashboardRoute,\n        isApiRoute,\n        requiresAuth: !isPublicRoute && !isApiRoute\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useAuthGuard.ts\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useErrorHandler.ts":
/*!**************************************!*\
  !*** ./src/hooks/useErrorHandler.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useErrorHandler: () => (/* binding */ useErrorHandler)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ useErrorHandler auto */ \n\nconst useErrorHandler = ()=>{\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    // Clear all states\n    const clearAll = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        setError(null);\n        setSuccess(null);\n        setIsLoading(false);\n    }, []);\n    // Clear error\n    const clearError = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        setError(null);\n    }, []);\n    // Clear success\n    const clearSuccess = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        setSuccess(null);\n    }, []);\n    // Set error with different types\n    const setErrorMessage = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((message, type = \"error\", code, details)=>{\n        setError({\n            message,\n            type,\n            code,\n            details\n        });\n        setSuccess(null) // Clear success when error occurs\n        ;\n    }, []);\n    // Set success message\n    const setSuccessMessage = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((message, autoHide = true)=>{\n        setSuccess({\n            message,\n            autoHide\n        });\n        setError(null) // Clear error when success occurs\n        ;\n        if (autoHide) {\n            setTimeout(()=>{\n                setSuccess(null);\n            }, 5000) // Auto-hide after 5 seconds\n            ;\n        }\n    }, []);\n    // Handle API errors with common patterns\n    const handleApiError = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((error, fallbackMessage = \"An unexpected error occurred\")=>{\n        console.error(\"API Error:\", error);\n        if (error?.response) {\n            // HTTP error response\n            const status = error.response.status;\n            const data = error.response.data;\n            switch(status){\n                case 401:\n                    setErrorMessage(\"Your session has expired. Please log in again.\", \"error\", \"UNAUTHORIZED\");\n                    // Redirect to login after a short delay\n                    setTimeout(()=>{\n                        router.push(\"/login\");\n                    }, 2000);\n                    break;\n                case 403:\n                    setErrorMessage(\"You do not have permission to perform this action.\", \"error\", \"FORBIDDEN\");\n                    break;\n                case 404:\n                    setErrorMessage(\"The requested resource was not found.\", \"error\", \"NOT_FOUND\");\n                    break;\n                case 422:\n                    setErrorMessage(data?.message || \"Please check your input and try again.\", \"error\", \"VALIDATION_ERROR\");\n                    break;\n                case 429:\n                    setErrorMessage(\"Too many requests. Please wait a moment and try again.\", \"error\", \"RATE_LIMIT\");\n                    break;\n                case 500:\n                    setErrorMessage(\"Server error. Please try again later.\", \"error\", \"SERVER_ERROR\");\n                    break;\n                default:\n                    setErrorMessage(data?.message || fallbackMessage, \"error\", `HTTP_${status}`);\n            }\n        } else if (error?.message) {\n            // Network or other errors\n            if (error.message.includes(\"fetch\")) {\n                setErrorMessage(\"Network error. Please check your connection and try again.\", \"error\", \"NETWORK_ERROR\");\n            } else {\n                setErrorMessage(error.message, \"error\", \"CLIENT_ERROR\");\n            }\n        } else {\n            setErrorMessage(fallbackMessage, \"error\", \"UNKNOWN_ERROR\");\n        }\n    }, [\n        setErrorMessage,\n        router\n    ]);\n    // Handle authentication errors specifically\n    const handleAuthError = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((error)=>{\n        console.error(\"Auth Error:\", error);\n        if (error?.message) {\n            if (error.message.includes(\"Invalid login credentials\")) {\n                setErrorMessage(\"Invalid email or password. Please try again.\", \"error\", \"INVALID_CREDENTIALS\");\n            } else if (error.message.includes(\"Email not confirmed\")) {\n                setErrorMessage(\"Please check your email and click the confirmation link.\", \"info\", \"EMAIL_NOT_CONFIRMED\");\n            } else if (error.message.includes(\"User already registered\")) {\n                setErrorMessage(\"An account with this email already exists. Please sign in instead.\", \"warning\", \"USER_EXISTS\");\n            } else if (error.message.includes(\"Password should be at least\")) {\n                setErrorMessage(\"Password must be at least 6 characters long.\", \"error\", \"WEAK_PASSWORD\");\n            } else {\n                setErrorMessage(error.message, \"error\", \"AUTH_ERROR\");\n            }\n        } else {\n            setErrorMessage(\"Authentication failed. Please try again.\", \"error\", \"AUTH_UNKNOWN\");\n        }\n    }, [\n        setErrorMessage\n    ]);\n    // Async operation wrapper with error handling\n    const withErrorHandling = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (operation, { loadingMessage, successMessage, errorMessage = \"Operation failed\" } = {})=>{\n        try {\n            setIsLoading(true);\n            clearAll();\n            const result = await operation();\n            if (successMessage) {\n                setSuccessMessage(successMessage);\n            }\n            return result;\n        } catch (error) {\n            handleApiError(error, errorMessage);\n            return null;\n        } finally{\n            setIsLoading(false);\n        }\n    }, [\n        clearAll,\n        setSuccessMessage,\n        handleApiError\n    ]);\n    // Retry mechanism\n    const retry = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (operation, maxRetries = 3, delay = 1000)=>{\n        let lastError;\n        for(let attempt = 1; attempt <= maxRetries; attempt++){\n            try {\n                return await operation();\n            } catch (error) {\n                lastError = error;\n                if (attempt < maxRetries) {\n                    // Wait before retrying\n                    await new Promise((resolve)=>setTimeout(resolve, delay * attempt));\n                }\n            }\n        }\n        handleApiError(lastError, `Operation failed after ${maxRetries} attempts`);\n        return null;\n    }, [\n        handleApiError\n    ]);\n    return {\n        // State\n        error,\n        success,\n        isLoading,\n        // Actions\n        clearAll,\n        clearError,\n        clearSuccess,\n        setErrorMessage,\n        setSuccessMessage,\n        setIsLoading,\n        // Handlers\n        handleApiError,\n        handleAuthError,\n        withErrorHandling,\n        retry,\n        // Computed\n        hasError: !!error,\n        hasSuccess: !!success\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useErrorHandler.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createSupabaseClient: () => (/* binding */ createSupabaseClient),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/supabase-js */ \"(ssr)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(ssr)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__);\n\n\nconst supabaseUrl = \"https://ermaaxnoyckezbjtegmq.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVybWFheG5veWNrZXpianRlZ21xIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEzMzI2NDgsImV4cCI6MjA2NjkwODY0OH0.ngJdxCneL3KSN-PxlSybKSplflElwH8PKD_J4-_gilI\";\n// Client-side Supabase client\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(supabaseUrl, supabaseAnonKey);\n// Component client for Next.js App Router\nconst createSupabaseClient = ()=>(0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__.createClientComponentClient)();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/supabase.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/styles/globals.css":
/*!************************************!*\
  !*** ./src/app/styles/globals.css ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"cd8cab21ee81\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL3N0eWxlcy9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLEtBQVUsRUFBRSxFQUF1QiIsInNvdXJjZXMiOlsid2VicGFjazovL3dvcmRwcmVzcy1haS1hcHAvLi9zcmMvYXBwL3N0eWxlcy9nbG9iYWxzLmNzcz8yNTZlIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiY2Q4Y2FiMjFlZTgxXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/styles/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/dashboard/layout.tsx":
/*!**************************************!*\
  !*** ./src/app/dashboard/layout.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./styles/globals.css */ \"(rsc)/./src/app/styles/globals.css\");\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/context/AuthContext */ \"(rsc)/./src/context/AuthContext.tsx\");\n/* harmony import */ var _components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ErrorBoundary */ \"(rsc)/./src/components/ErrorBoundary.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"WordPress AI Builder\",\n    description: \"Automated WordPress site generation with AI\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_3__.ErrorBoundary, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/layout.tsx\",\n                    lineNumber: 15,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/layout.tsx\",\n                lineNumber: 14,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/layout.tsx\",\n            lineNumber: 13,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/layout.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFBOEI7QUFDdUI7QUFDTTtBQUVwRCxNQUFNRSxXQUFXO0lBQ3RCQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFFO0FBRWEsU0FBU0MsV0FBVyxFQUFFQyxRQUFRLEVBQWlDO0lBQzVFLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztzQkFDQyw0RUFBQ1Isb0VBQWFBOzBCQUNaLDRFQUFDRCw4REFBWUE7OEJBQ1ZNOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFNYiIsInNvdXJjZXMiOlsid2VicGFjazovL3dvcmRwcmVzcy1haS1hcHAvLi9zcmMvYXBwL2xheW91dC50c3g/NTdhOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgJy4vc3R5bGVzL2dsb2JhbHMuY3NzJztcbmltcG9ydCB7IEF1dGhQcm92aWRlciB9IGZyb20gJ0AvY29udGV4dC9BdXRoQ29udGV4dCc7XG5pbXBvcnQgeyBFcnJvckJvdW5kYXJ5IH0gZnJvbSAnQC9jb21wb25lbnRzL0Vycm9yQm91bmRhcnknO1xuXG5leHBvcnQgY29uc3QgbWV0YWRhdGEgPSB7XG4gIHRpdGxlOiAnV29yZFByZXNzIEFJIEJ1aWxkZXInLFxuICBkZXNjcmlwdGlvbjogJ0F1dG9tYXRlZCBXb3JkUHJlc3Mgc2l0ZSBnZW5lcmF0aW9uIHdpdGggQUknLFxufTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7IGNoaWxkcmVuIH06IHsgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZSB9KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImVuXCI+XG4gICAgICA8Ym9keT5cbiAgICAgICAgPEVycm9yQm91bmRhcnk+XG4gICAgICAgICAgPEF1dGhQcm92aWRlcj5cbiAgICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgICA8L0F1dGhQcm92aWRlcj5cbiAgICAgICAgPC9FcnJvckJvdW5kYXJ5PlxuICAgICAgPC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJBdXRoUHJvdmlkZXIiLCJFcnJvckJvdW5kYXJ5IiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/ErrorBoundary.tsx":
/*!******************************************!*\
  !*** ./src/components/ErrorBoundary.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ErrorBoundary: () => (/* binding */ e0),
/* harmony export */   ErrorDisplay: () => (/* binding */ e1),
/* harmony export */   Loading: () => (/* binding */ e3),
/* harmony export */   SuccessDisplay: () => (/* binding */ e2)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Desktop/wp-ai-app/src/components/ErrorBoundary.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Desktop/wp-ai-app/src/components/ErrorBoundary.tsx#ErrorBoundary`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Desktop/wp-ai-app/src/components/ErrorBoundary.tsx#ErrorDisplay`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Desktop/wp-ai-app/src/components/ErrorBoundary.tsx#SuccessDisplay`);

const e3 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Desktop/wp-ai-app/src/components/ErrorBoundary.tsx#Loading`);


/***/ }),

/***/ "(rsc)/./src/context/AuthContext.tsx":
/*!*************************************!*\
  !*** ./src/context/AuthContext.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ e0),
/* harmony export */   useAuth: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Desktop/wp-ai-app/src/context/AuthContext.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Desktop/wp-ai-app/src/context/AuthContext.tsx#AuthProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Desktop/wp-ai-app/src/context/AuthContext.tsx#useAuth`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/isows","vendor-chunks/lucide-react","vendor-chunks/set-cookie-parser","vendor-chunks/jose"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();