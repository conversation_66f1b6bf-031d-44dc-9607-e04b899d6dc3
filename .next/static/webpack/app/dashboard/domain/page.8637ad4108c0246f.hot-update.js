"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/domain/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/domain/page.tsx":
/*!*******************************************!*\
  !*** ./src/app/dashboard/domain/page.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(app-pages-browser)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst DomainPage = ()=>{\n    var _sites_find;\n    _s();\n    const [domainName, setDomainName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [results, setResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [registering, setRegistering] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [sites, setSites] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedSiteId, setSelectedSiteId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [sitesLoading, setSitesLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__.createClientComponentClient)();\n    // Fetch sites on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchSites = async ()=>{\n            try {\n                const { data, error } = await supabase.from(\"user-websites\").select(\"id, site_name, expiry_status, expiry_time\");\n                if (error) {\n                    throw error;\n                }\n                setSites(data);\n            } catch (err) {\n                console.error(\"Error fetching sites:\", err);\n            } finally{\n                setSitesLoading(false);\n            }\n        };\n        fetchSites();\n    }, [\n        supabase\n    ]);\n    const handleSearch = async (e)=>{\n        e.preventDefault();\n        if (!domainName.trim()) return;\n        setLoading(true);\n        setResults([]);\n        try {\n            const response = await fetch(\"/api/namecheap\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    domain: domainName.trim(),\n                    action: \"check\"\n                })\n            });\n            if (!response.ok) {\n                const { error } = await response.json();\n                throw new Error(error || \"Failed to check domain availability.\");\n            }\n            const data = await response.json();\n            setResults(data.results);\n            console.log(\"data.results\", data.results);\n        } catch (error) {\n            const err = error;\n            console.error(\"Domain search error:\", err);\n            setResults([\n                {\n                    Domain: domainName,\n                    Available: false,\n                    IsPremiumName: false,\n                    Error: err.message\n                }\n            ]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleRegister = async (domain)=>{\n        if (!selectedSiteId) {\n            alert(\"Please select a site to associate with this domain.\");\n            return;\n        }\n        setRegistering(domain);\n        try {\n            const response = await fetch(\"/api/namecheap\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    domain,\n                    action: \"register\",\n                    siteId: selectedSiteId\n                })\n            });\n            if (!response.ok) {\n                const { error } = await response.json();\n                throw new Error(error || \"Failed to register domain.\");\n            }\n            await response.json();\n            // Update the results to show the domain as registered\n            setResults((prev)=>prev.map((result)=>result.Domain === domain ? {\n                        ...result,\n                        Available: false,\n                        Error: undefined\n                    } : result));\n            const selectedSite = sites.find((site)=>site.id === selectedSiteId);\n            alert(\"Domain \".concat(domain, \" has been successfully registered and will be associated with \").concat(selectedSite === null || selectedSite === void 0 ? void 0 : selectedSite.site_name, \"!\"));\n        } catch (error) {\n            const err = error;\n            console.error(\"Domain registration error:\", err);\n            alert(\"Failed to register \".concat(domain, \": \").concat(err.message));\n        } finally{\n            setRegistering(null);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen p-8 bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"mb-4 text-4xl font-bold text-gray-800\",\n                    children: \"Register a Domain\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mb-8 text-gray-600\",\n                    children: \"Find and register the perfect domain for your new website.\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                    lineNumber: 133,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 mb-8 bg-white rounded-lg shadow-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"mb-4 text-xl font-semibold text-gray-800\",\n                            children: \"Select Site\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mb-4 text-gray-600\",\n                            children: \"Choose which site you want to associate with your new domain.\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 11\n                        }, undefined),\n                        sitesLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-gray-500\",\n                            children: \"Loading sites...\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 13\n                        }, undefined) : sites.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-gray-500\",\n                            children: \"No sites available. Please create a site first.\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                            lineNumber: 143,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: selectedSiteId,\n                            onChange: (e)=>setSelectedSiteId(e.target.value),\n                            className: \"w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"\",\n                                    children: \"Select a site...\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 15\n                                }, undefined),\n                                sites.map((site)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: site.id,\n                                        children: [\n                                            site.site_name,\n                                            \" (\",\n                                            site.expiry_status,\n                                            \")\"\n                                        ]\n                                    }, site.id, true, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 17\n                                    }, undefined))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 mb-8 bg-white rounded-lg shadow-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"mb-4 text-xl font-semibold text-gray-800\",\n                            children: \"Search for Domain\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mb-4 text-gray-600\",\n                            children: \"Enter a domain name to check availability across multiple extensions.\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 11\n                        }, undefined),\n                        !selectedSiteId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-3 mb-4 border rounded-md text-amber-700 bg-amber-50 border-amber-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm\",\n                                children: \"⚠️ Please select a site above before searching for domains.\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: domainName,\n                                    onChange: (e)=>setDomainName(e.target.value),\n                                    placeholder: \"Find your new domain (e.g., my-awesome-site)\",\n                                    className: \"flex-grow p-3 transition border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500\",\n                                    onKeyDown: (e)=>e.key === \"Enter\" && handleSearch(e)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleSearch,\n                                    disabled: loading || !domainName.trim() || !selectedSiteId,\n                                    className: \"px-6 py-3 font-bold text-white transition bg-green-600 rounded-md hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed\",\n                                    title: !selectedSiteId ? \"Please select a site first\" : \"\",\n                                    children: loading ? \"Searching...\" : \"Search\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                    lineNumber: 160,\n                    columnNumber: 9\n                }, undefined),\n                results.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 bg-white rounded-lg shadow-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-gray-800\",\n                                    children: \"Results\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 15\n                                }, undefined),\n                                selectedSiteId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: [\n                                        \"Will be associated with: \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: (_sites_find = sites.find((s)=>s.id === selectedSiteId)) === null || _sites_find === void 0 ? void 0 : _sites_find.site_name\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 44\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                            lineNumber: 192,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: \"space-y-4\",\n                            children: results.map((result)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"flex items-center justify-between p-4 border rounded-md\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-lg font-medium text-gray-700\",\n                                                    children: result.Domain\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                                    lineNumber: 204,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                result.Price && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: [\n                                                        \"AUD $\",\n                                                        result.Price.toFixed(2),\n                                                        \"/year\",\n                                                        result.IsPremiumName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-2 font-semibold text-orange-500\",\n                                                            children: \"Premium\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                                            lineNumber: 208,\n                                                            columnNumber: 50\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                                    lineNumber: 206,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-4\",\n                                            children: result.Error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-semibold text-red-500\",\n                                                children: result.Error\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 23\n                                            }, undefined) : result.Available ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-bold text-green-600\",\n                                                        children: \"Available!\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                                        lineNumber: 217,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleRegister(result.Domain),\n                                                        disabled: registering === result.Domain,\n                                                        className: \"px-4 py-2 font-bold text-white transition bg-blue-600 rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed min-w-[100px]\",\n                                                        children: registering === result.Domain ? \"Registering...\" : \"Register\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                                        lineNumber: 218,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-semibold text-red-500\",\n                                                children: \"Unavailable\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, result.Domain, true, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                    lineNumber: 191,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n            lineNumber: 131,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n        lineNumber: 130,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DomainPage, \"hSqFAkCxunyLypwhbc2cIc3MsYs=\");\n_c = DomainPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DomainPage);\nvar _c;\n$RefreshReg$(_c, \"DomainPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/domain/page.tsx\n"));

/***/ })

});