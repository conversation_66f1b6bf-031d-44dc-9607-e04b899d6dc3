"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/domain/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/domain/page.tsx":
/*!*******************************************!*\
  !*** ./src/app/dashboard/domain/page.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(app-pages-browser)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst DomainPage = ()=>{\n    var _sites_find;\n    _s();\n    const [domainName, setDomainName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [results, setResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // ✨ This state now indicates that we are redirecting to payment\n    const [isRedirecting, setIsRedirecting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [sites, setSites] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedSiteId, setSelectedSiteId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [sitesLoading, setSitesLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__.createClientComponentClient)();\n    // Fetch sites on component mount (Unchanged)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchSites = async ()=>{\n            try {\n                const { data, error } = await supabase.from(\"user-websites\").select(\"id, site_name, expiry_status, expiry_time\");\n                if (error) {\n                    throw error;\n                }\n                setSites(data);\n            } catch (err) {\n                console.error(\"Error fetching sites:\", err);\n            } finally{\n                setSitesLoading(false);\n            }\n        };\n        fetchSites();\n    }, [\n        supabase\n    ]);\n    // handleSearch function (Unchanged)\n    const handleSearch = async (e)=>{\n        e.preventDefault();\n        if (!domainName.trim()) return;\n        setLoading(true);\n        setResults([]);\n        try {\n            const response = await fetch(\"/api/namecheap\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    domain: domainName.trim(),\n                    action: \"check\"\n                })\n            });\n            if (!response.ok) {\n                const { error } = await response.json();\n                throw new Error(error || \"Failed to check domain availability.\");\n            }\n            const data = await response.json();\n            setResults(data.results);\n        } catch (error) {\n            const err = error;\n            console.error(\"Domain search error:\", err);\n            setResults([\n                {\n                    Domain: domainName,\n                    Available: false,\n                    IsPremiumName: false,\n                    Error: err.message\n                }\n            ]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // ✨ MODIFIED: This function now initiates the payment flow instead of direct registration.\n    const handleInitiatePayment = async (domain, price)=>{\n        if (!selectedSiteId) {\n            alert(\"Please select a site to associate with this domain.\");\n            return;\n        }\n        if (price === undefined) {\n            alert(\"Cannot proceed without a price.\");\n            return;\n        }\n        setIsRedirecting(domain);\n        try {\n            // Call the backend to create a Stripe Checkout session\n            const response = await fetch(\"/api/namecheap\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    domain,\n                    price,\n                    action: \"create-payment-session\",\n                    siteId: selectedSiteId\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || \"Failed to create payment session.\");\n            }\n            // Redirect the user to the Stripe Checkout page\n            if (data.url) {\n                window.location.href = data.url;\n            } else {\n                throw new Error(\"Did not receive a checkout URL from the server.\");\n            }\n        } catch (error) {\n            const err = error;\n            console.error(\"Payment initiation error:\", err);\n            alert(\"Could not start the payment process: \".concat(err.message));\n            // Reset state only on error, as success will navigate away\n            setIsRedirecting(null);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen p-8 bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"mb-4 text-4xl font-bold text-gray-800\",\n                    children: \"Register a Domain\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mb-8 text-gray-600\",\n                    children: \"Find and register the perfect domain for your new website.\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 mb-8 bg-white rounded-lg shadow-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"mb-4 text-xl font-semibold text-gray-800\",\n                            children: \"Select Site\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mb-4 text-gray-600\",\n                            children: \"Choose which site you want to associate with your new domain.\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                            lineNumber: 143,\n                            columnNumber: 11\n                        }, undefined),\n                        sitesLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-gray-500\",\n                            children: \"Loading sites...\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 13\n                        }, undefined) : sites.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-gray-500\",\n                            children: \"No sites available. Please create a site first.\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: selectedSiteId,\n                            onChange: (e)=>setSelectedSiteId(e.target.value),\n                            className: \"w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"\",\n                                    children: \"Select a site...\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 15\n                                }, undefined),\n                                sites.map((site)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: site.id,\n                                        children: [\n                                            site.site_name,\n                                            \" (\",\n                                            site.expiry_status,\n                                            \")\"\n                                        ]\n                                    }, site.id, true, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 17\n                                    }, undefined))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                    lineNumber: 141,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 mb-8 bg-white rounded-lg shadow-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"mb-4 text-xl font-semibold text-gray-800\",\n                            children: \"Search for Domain\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mb-4 text-gray-600\",\n                            children: \"Enter a domain name to check availability across multiple extensions.\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                            lineNumber: 168,\n                            columnNumber: 11\n                        }, undefined),\n                        !selectedSiteId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-3 mb-4 border rounded-md text-amber-700 bg-amber-50 border-amber-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm\",\n                                children: \"⚠️ Please select a site above before searching for domains.\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: domainName,\n                                    onChange: (e)=>setDomainName(e.target.value),\n                                    placeholder: \"Find your new domain (e.g., my-awesome-site)\",\n                                    className: \"flex-grow p-3 transition border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500\",\n                                    onKeyDown: (e)=>e.key === \"Enter\" && handleSearch(e)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleSearch,\n                                    disabled: loading || !domainName.trim() || !selectedSiteId,\n                                    className: \"px-6 py-3 font-bold text-white transition bg-green-600 rounded-md hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed\",\n                                    title: !selectedSiteId ? \"Please select a site first\" : \"\",\n                                    children: loading ? \"Searching...\" : \"Search\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                    lineNumber: 166,\n                    columnNumber: 9\n                }, undefined),\n                results.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 bg-white rounded-lg shadow-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-gray-800\",\n                                    children: \"Results\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 15\n                                }, undefined),\n                                selectedSiteId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: [\n                                        \"Will be associated with: \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: (_sites_find = sites.find((s)=>s.id === selectedSiteId)) === null || _sites_find === void 0 ? void 0 : _sites_find.site_name\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 44\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                            lineNumber: 199,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: \"space-y-4\",\n                            children: results.map((result)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"flex items-center justify-between p-4 border rounded-md\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-lg font-medium text-gray-700\",\n                                                    children: result.Domain\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                                    lineNumber: 211,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                result.Price && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: [\n                                                        \"AUD $\",\n                                                        result.Price.toFixed(2),\n                                                        \"/year\",\n                                                        result.IsPremiumName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-2 font-semibold text-orange-500\",\n                                                            children: \"Premium\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                                            lineNumber: 215,\n                                                            columnNumber: 50\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                                    lineNumber: 213,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-4\",\n                                            children: result.Error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-semibold text-red-500\",\n                                                children: result.Error\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 23\n                                            }, undefined) : result.Available ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-bold text-green-600\",\n                                                        children: \"Available!\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                                        lineNumber: 224,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleInitiatePayment(result.Domain, result.Price),\n                                                        disabled: isRedirecting === result.Domain,\n                                                        className: \"px-4 py-2 font-bold text-white transition bg-blue-600 rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed min-w-[110px]\",\n                                                        children: isRedirecting === result.Domain ? \"Redirecting...\" : \"Pay & Register\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                                        lineNumber: 226,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-semibold text-red-500\",\n                                                children: \"Unavailable\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                                lineNumber: 235,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, result.Domain, true, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                            lineNumber: 207,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                    lineNumber: 198,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n            lineNumber: 136,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n        lineNumber: 135,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DomainPage, \"O7qTZzbm4YfIHvzdiawVlsE4avc=\");\n_c = DomainPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DomainPage);\nvar _c;\n$RefreshReg$(_c, \"DomainPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/domain/page.tsx\n"));

/***/ })

});