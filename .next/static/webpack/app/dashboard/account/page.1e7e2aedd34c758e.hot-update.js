"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/account/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/account/page.tsx":
/*!********************************************!*\
  !*** ./src/app/dashboard/account/page.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ProfilePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Calendar_Camera_Edit3_Lock_Mail_MapPin_Phone_Save_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Camera,Edit3,Lock,Mail,MapPin,Phone,Save,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Camera_Edit3_Lock_Mail_MapPin_Phone_Save_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Camera,Edit3,Lock,Mail,MapPin,Phone,Save,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Camera_Edit3_Lock_Mail_MapPin_Phone_Save_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Camera,Edit3,Lock,Mail,MapPin,Phone,Save,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen-line.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Camera_Edit3_Lock_Mail_MapPin_Phone_Save_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Camera,Edit3,Lock,Mail,MapPin,Phone,Save,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Camera_Edit3_Lock_Mail_MapPin_Phone_Save_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Camera,Edit3,Lock,Mail,MapPin,Phone,Save,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Camera_Edit3_Lock_Mail_MapPin_Phone_Save_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Camera,Edit3,Lock,Mail,MapPin,Phone,Save,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Camera_Edit3_Lock_Mail_MapPin_Phone_Save_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Camera,Edit3,Lock,Mail,MapPin,Phone,Save,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Camera_Edit3_Lock_Mail_MapPin_Phone_Save_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Camera,Edit3,Lock,Mail,MapPin,Phone,Save,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Camera_Edit3_Lock_Mail_MapPin_Phone_Save_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Camera,Edit3,Lock,Mail,MapPin,Phone,Save,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Camera_Edit3_Lock_Mail_MapPin_Phone_Save_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Camera,Edit3,Lock,Mail,MapPin,Phone,Save,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _hooks_useAuthGuard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useAuthGuard */ \"(app-pages-browser)/./src/hooks/useAuthGuard.ts\");\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/context/AuthContext */ \"(app-pages-browser)/./src/context/AuthContext.tsx\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction ProfilePage() {\n    _s();\n    const [isEditing, setIsEditing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Ensure user is authenticated\n    const { user } = (0,_hooks_useAuthGuard__WEBPACK_IMPORTED_MODULE_2__.useRequireAuth)();\n    const { updateProfile } = (0,_context_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_4__.createSupabaseClient)();\n    const [profileData, setProfileData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        email: \"\",\n        phone: \"\",\n        location: \"\",\n        joinDate: \"\",\n        avatar: null\n    });\n    const [settings, setSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        emailNotifications: true,\n        pushNotifications: false,\n        twoFactorAuth: false\n    });\n    // Load user profile data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadProfile = async ()=>{\n            if (!user) {\n                setLoading(false);\n                return;\n            }\n            try {\n                var _user_email;\n                const { data: { session } } = await supabase.auth.getSession();\n                if (!session) {\n                    throw new Error(\"No active session\");\n                }\n                const response = await fetch(\"/api/user/profile\", {\n                    headers: {\n                        \"Authorization\": \"Bearer \".concat(session.access_token),\n                        \"Content-Type\": \"application/json\"\n                    }\n                });\n                if (!response.ok) {\n                    throw new Error(\"Failed to fetch profile\");\n                }\n                const { profile } = await response.json();\n                setProfileData({\n                    name: (profile === null || profile === void 0 ? void 0 : profile.full_name) || ((_user_email = user.email) === null || _user_email === void 0 ? void 0 : _user_email.split(\"@\")[0]) || \"\",\n                    email: user.email || \"\",\n                    phone: \"\",\n                    location: \"\",\n                    joinDate: new Date(user.created_at || \"\").toLocaleDateString(\"en-US\", {\n                        year: \"numeric\",\n                        month: \"long\"\n                    }),\n                    avatar: (profile === null || profile === void 0 ? void 0 : profile.avatar_url) || null\n                });\n            } catch (err) {\n                console.error(\"Error loading profile:\", err);\n                setError(err.message);\n            } finally{\n                setLoading(false);\n            }\n        };\n        loadProfile();\n    }, [\n        user,\n        supabase\n    ]);\n    const handleSave = async ()=>{\n        if (!user) return;\n        setSaving(true);\n        setError(null);\n        try {\n            // Update auth profile\n            const { error: authError } = await updateProfile({\n                full_name: profileData.name,\n                avatar_url: profileData.avatar\n            });\n            if (authError) {\n                throw new Error(authError);\n            }\n            // Update database profile\n            const { data: { session } } = await supabase.auth.getSession();\n            if (session) {\n                const response = await fetch(\"/api/user/profile\", {\n                    method: \"PUT\",\n                    headers: {\n                        \"Authorization\": \"Bearer \".concat(session.access_token),\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        full_name: profileData.name,\n                        avatar_url: profileData.avatar\n                    })\n                });\n                if (!response.ok) {\n                    throw new Error(\"Failed to update profile\");\n                }\n            }\n            setIsEditing(false);\n        } catch (err) {\n            console.error(\"Error saving profile:\", err);\n            setError(err.message);\n        } finally{\n            setSaving(false);\n        }\n    };\n    const handleInputChange = (field, value)=>{\n        setProfileData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    const handleSettingChange = (setting, value)=>{\n        setSettings((prev)=>({\n                ...prev,\n                [setting]: value\n            }));\n    };\n    // Show loading state\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-slate-600\",\n                        children: \"Loading profile...\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                lineNumber: 150,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n            lineNumber: 149,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b bg-white/80 backdrop-blur-sm border-slate-200/50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl px-6 py-4 mx-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold text-transparent bg-gradient-to-r from-slate-900 to-slate-600 bg-clip-text\",\n                                        children: \"Profile Settings\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-1 text-slate-600\",\n                                        children: \"Manage your account and preferences\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setIsEditing(false),\n                                            className: \"flex items-center gap-2 px-4 py-2 transition-all duration-200 rounded-lg text-slate-600 hover:text-slate-800 hover:bg-slate-100\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Camera_Edit3_Lock_Mail_MapPin_Phone_Save_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Cancel\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleSave,\n                                            disabled: saving,\n                                            className: \"flex items-center gap-2 px-4 py-2 text-white transition-all duration-200 rounded-lg shadow-lg bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 shadow-blue-500/25 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Camera_Edit3_Lock_Mail_MapPin_Phone_Save_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 21\n                                                }, this),\n                                                saving ? \"Saving...\" : \"Save Changes\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setIsEditing(true),\n                                    className: \"flex items-center gap-2 px-4 py-2 text-white transition-all duration-200 rounded-lg shadow-lg bg-gradient-to-r from-slate-800 to-slate-600 hover:from-slate-900 hover:to-slate-700 shadow-slate-500/25\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Camera_Edit3_Lock_Mail_MapPin_Phone_Save_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                            lineNumber: 194,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"Edit Profile\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                    lineNumber: 162,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                lineNumber: 161,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl px-6 py-8 mx-auto\",\n                children: [\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-600 text-sm\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                            lineNumber: 207,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 gap-8 lg:grid-cols-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6 border shadow-xl bg-white/70 backdrop-blur-sm rounded-2xl shadow-slate-200/50 border-white/50\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col items-center text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative group\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center w-20 h-20 text-2xl font-bold text-white rounded-full shadow-lg bg-gradient-to-br from-blue-500 to-indigo-600\",\n                                                        children: profileData.avatar ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: profileData.avatar,\n                                                            alt: \"Profile\",\n                                                            className: \"object-cover w-full h-full rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                            lineNumber: 219,\n                                                            columnNumber: 23\n                                                        }, this) : profileData.name.split(\" \").map((n)=>n[0]).join(\"\")\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                        lineNumber: 217,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    isEditing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"absolute flex items-center justify-center w-8 h-8 transition-colors bg-white border-2 border-blue-500 rounded-full shadow-lg -bottom-2 -right-2 hover:bg-blue-50\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Camera_Edit3_Lock_Mail_MapPin_Phone_Save_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            size: 14,\n                                                            className: \"text-blue-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                            lineNumber: 226,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                        lineNumber: 225,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                lineNumber: 216,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"mt-3 text-lg font-semibold text-slate-800\",\n                                                children: profileData.name\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-slate-600\",\n                                                children: profileData.email\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                lineNumber: 231,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"overflow-hidden border shadow-xl bg-white/70 backdrop-blur-sm rounded-2xl shadow-slate-200/50 border-white/50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-8 border-b border-slate-200/50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"mb-2 text-xl font-semibold text-slate-800\",\n                                                            children: \"Personal Information\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                            lineNumber: 243,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-slate-600\",\n                                                            children: \"Update your personal details and profile information\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                            lineNumber: 244,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 gap-6 md:grid-cols-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"block mb-2 text-sm font-medium text-slate-700\",\n                                                                            children: \"Full Name\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                            lineNumber: 250,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"relative\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Camera_Edit3_Lock_Mail_MapPin_Phone_Save_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                                    className: \"absolute w-5 h-5 left-3 top-3 text-slate-400\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                                    lineNumber: 252,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: \"text\",\n                                                                                    value: profileData.name,\n                                                                                    onChange: (e)=>handleInputChange(\"name\", e.target.value),\n                                                                                    disabled: !isEditing,\n                                                                                    className: \"w-full py-3 pl-10 pr-4 transition-all duration-200 border border-slate-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white/50 backdrop-blur-sm disabled:bg-slate-50/50 disabled:text-slate-500\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                                    lineNumber: 253,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                            lineNumber: 251,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                    lineNumber: 249,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"block mb-2 text-sm font-medium text-slate-700\",\n                                                                            children: \"Email Address\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                            lineNumber: 264,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"relative\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Camera_Edit3_Lock_Mail_MapPin_Phone_Save_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                                    className: \"absolute w-5 h-5 left-3 top-3 text-slate-400\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                                    lineNumber: 266,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: \"email\",\n                                                                                    value: profileData.email,\n                                                                                    onChange: (e)=>handleInputChange(\"email\", e.target.value),\n                                                                                    disabled: !isEditing,\n                                                                                    className: \"w-full py-3 pl-10 pr-4 transition-all duration-200 border border-slate-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white/50 backdrop-blur-sm disabled:bg-slate-50/50 disabled:text-slate-500\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                                    lineNumber: 267,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                            lineNumber: 265,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                    lineNumber: 263,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"block mb-2 text-sm font-medium text-slate-700\",\n                                                                            children: \"Phone Number\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                            lineNumber: 278,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"relative\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Camera_Edit3_Lock_Mail_MapPin_Phone_Save_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                    className: \"absolute w-5 h-5 left-3 top-3 text-slate-400\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                                    lineNumber: 280,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: \"tel\",\n                                                                                    value: profileData.phone,\n                                                                                    onChange: (e)=>handleInputChange(\"phone\", e.target.value),\n                                                                                    disabled: !isEditing,\n                                                                                    className: \"w-full py-3 pl-10 pr-4 transition-all duration-200 border border-slate-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white/50 backdrop-blur-sm disabled:bg-slate-50/50 disabled:text-slate-500\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                                    lineNumber: 281,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                            lineNumber: 279,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                    lineNumber: 277,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"block mb-2 text-sm font-medium text-slate-700\",\n                                                                            children: \"Location\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                            lineNumber: 292,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"relative\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Camera_Edit3_Lock_Mail_MapPin_Phone_Save_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                    className: \"absolute w-5 h-5 left-3 top-3 text-slate-400\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                                    lineNumber: 294,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: \"text\",\n                                                                                    value: profileData.location,\n                                                                                    onChange: (e)=>handleInputChange(\"location\", e.target.value),\n                                                                                    disabled: !isEditing,\n                                                                                    className: \"w-full py-3 pl-10 pr-4 transition-all duration-200 border border-slate-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white/50 backdrop-blur-sm disabled:bg-slate-50/50 disabled:text-slate-500\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                                    lineNumber: 295,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                            lineNumber: 293,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                    lineNumber: 291,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                            lineNumber: 248,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2 p-4 text-sm text-slate-600 bg-slate-50/50 rounded-xl\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Camera_Edit3_Lock_Mail_MapPin_Phone_Save_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    size: 16\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                    lineNumber: 307,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        \"Member since \",\n                                                                        profileData.joinDate\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                    lineNumber: 308,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                            lineNumber: 306,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                    lineNumber: 247,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-8 border-b border-slate-200/50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"mb-2 text-xl font-semibold text-slate-800\",\n                                                            children: \"Security Settings\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                            lineNumber: 316,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-slate-600\",\n                                                            children: \"Manage your account security and authentication\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                            lineNumber: 317,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                    lineNumber: 315,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-6 border border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                className: \"font-semibold text-slate-800\",\n                                                                                children: \"Two-Factor Authentication\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                                lineNumber: 324,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"mt-1 text-sm text-slate-600\",\n                                                                                children: \"Add an extra layer of security to your account\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                                lineNumber: 325,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                        lineNumber: 323,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"checkbox\",\n                                                                                checked: settings.twoFactorAuth,\n                                                                                onChange: (e)=>handleSettingChange(\"twoFactorAuth\", e.target.checked),\n                                                                                className: \"sr-only\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                                lineNumber: 328,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>handleSettingChange(\"twoFactorAuth\", !settings.twoFactorAuth),\n                                                                                className: \"relative inline-flex h-6 w-11 items-center rounded-full transition-colors \".concat(settings.twoFactorAuth ? \"bg-blue-600\" : \"bg-slate-300\"),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"inline-block h-4 w-4 transform rounded-full bg-white transition-transform \".concat(settings.twoFactorAuth ? \"translate-x-6\" : \"translate-x-1\")\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                                    lineNumber: 340,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                                lineNumber: 334,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                        lineNumber: 327,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                lineNumber: 322,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                            lineNumber: 321,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-6 border bg-white/50 rounded-xl border-slate-200\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"mb-4 font-semibold text-slate-800\",\n                                                                    children: \"Change Password\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                    lineNumber: 351,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                    className: \"block mb-2 text-sm font-medium text-slate-700\",\n                                                                                    children: \"Current Password\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                                    lineNumber: 354,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"relative\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Camera_Edit3_Lock_Mail_MapPin_Phone_Save_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                            className: \"absolute w-5 h-5 left-3 top-3 text-slate-400\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                                            lineNumber: 356,\n                                                                                            columnNumber: 27\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                            type: \"password\",\n                                                                                            className: \"w-full py-3 pl-10 pr-4 transition-all duration-200 border border-slate-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                                                                                            placeholder: \"Enter current password\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                                            lineNumber: 357,\n                                                                                            columnNumber: 27\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                                    lineNumber: 355,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                            lineNumber: 353,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                    className: \"block mb-2 text-sm font-medium text-slate-700\",\n                                                                                    children: \"New Password\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                                    lineNumber: 365,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"relative\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Camera_Edit3_Lock_Mail_MapPin_Phone_Save_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                            className: \"absolute w-5 h-5 left-3 top-3 text-slate-400\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                                            lineNumber: 367,\n                                                                                            columnNumber: 27\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                            type: \"password\",\n                                                                                            className: \"w-full py-3 pl-10 pr-4 transition-all duration-200 border border-slate-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                                                                                            placeholder: \"Enter new password\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                                            lineNumber: 368,\n                                                                                            columnNumber: 27\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                                    lineNumber: 366,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                            lineNumber: 364,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            className: \"px-4 py-2 text-white transition-all duration-200 rounded-lg bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700\",\n                                                                            children: \"Update Password\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                            lineNumber: 375,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                    lineNumber: 352,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                            lineNumber: 350,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                    lineNumber: 320,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                            lineNumber: 314,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-8 border-b border-slate-200/50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"mb-2 text-xl font-semibold text-slate-800\",\n                                                            children: \"Notification Preferences\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                            lineNumber: 386,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-slate-600\",\n                                                            children: \"Choose how you want to be notified\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                            lineNumber: 387,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                    lineNumber: 385,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-6 border bg-white/50 rounded-xl border-slate-200\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between mb-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                className: \"font-semibold text-slate-800\",\n                                                                                children: \"Email Notifications\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                                lineNumber: 394,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"mt-1 text-sm text-slate-600\",\n                                                                                children: \"Receive notifications via email\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                                lineNumber: 395,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                        lineNumber: 393,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleSettingChange(\"emailNotifications\", !settings.emailNotifications),\n                                                                        className: \"relative inline-flex h-6 w-11 items-center rounded-full transition-colors \".concat(settings.emailNotifications ? \"bg-blue-600\" : \"bg-slate-300\"),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"inline-block h-4 w-4 transform rounded-full bg-white transition-transform \".concat(settings.emailNotifications ? \"translate-x-6\" : \"translate-x-1\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                            lineNumber: 403,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                        lineNumber: 397,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                lineNumber: 392,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                            lineNumber: 391,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-6 border bg-white/50 rounded-xl border-slate-200\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between mb-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                className: \"font-semibold text-slate-800\",\n                                                                                children: \"Push Notifications\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                                lineNumber: 415,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"mt-1 text-sm text-slate-600\",\n                                                                                children: \"Receive push notifications on your device\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                                lineNumber: 416,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                        lineNumber: 414,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleSettingChange(\"pushNotifications\", !settings.pushNotifications),\n                                                                        className: \"relative inline-flex h-6 w-11 items-center rounded-full transition-colors \".concat(settings.pushNotifications ? \"bg-blue-600\" : \"bg-slate-300\"),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"inline-block h-4 w-4 transform rounded-full bg-white transition-transform \".concat(settings.pushNotifications ? \"translate-x-6\" : \"translate-x-1\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                            lineNumber: 424,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                        lineNumber: 418,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                lineNumber: 413,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                            lineNumber: 412,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                    lineNumber: 390,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                            lineNumber: 384,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"mb-2 text-xl font-semibold text-slate-800\",\n                                                            children: \"Account Management\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                            lineNumber: 438,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-slate-600\",\n                                                            children: \"Manage your account settings\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                            lineNumber: 439,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                    lineNumber: 437,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-6 border border-red-200 bg-red-50 rounded-xl\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"mb-2 font-semibold text-red-800\",\n                                                            children: \"Delete Account\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                            lineNumber: 443,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"mb-4 text-sm text-red-600\",\n                                                            children: \"Permanently delete your account and all associated data\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                            lineNumber: 444,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"px-4 py-2 text-white transition-colors bg-red-600 rounded-lg hover:bg-red-700\",\n                                                            children: \"Delete Account\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                            lineNumber: 445,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                    lineNumber: 442,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                            lineNumber: 436,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                    lineNumber: 238,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                lineNumber: 203,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n        lineNumber: 159,\n        columnNumber: 5\n    }, this);\n}\n_s(ProfilePage, \"cLaku2HbnfzpqaNU6hVYsxPq5QA=\", false, function() {\n    return [\n        _hooks_useAuthGuard__WEBPACK_IMPORTED_MODULE_2__.useRequireAuth,\n        _context_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = ProfilePage;\nvar _c;\n$RefreshReg$(_c, \"ProfilePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/account/page.tsx\n"));

/***/ })

});