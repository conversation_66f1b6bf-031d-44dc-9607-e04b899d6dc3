"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/account/page",{

/***/ "(app-pages-browser)/./src/context/AuthContext.tsx":
/*!*************************************!*\
  !*** ./src/context/AuthContext.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: function() { return /* binding */ AuthProvider; },\n/* harmony export */   useAuth: function() { return /* binding */ useAuth; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n// Create the auth context with default values\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    user: null,\n    session: null,\n    isLoading: true,\n    error: null,\n    signIn: async ()=>({\n            error: null\n        }),\n    signUp: async ()=>({\n            error: null\n        }),\n    signOut: async ()=>{},\n    resetPassword: async ()=>({\n            error: null\n        }),\n    updateProfile: async ()=>({\n            error: null\n        })\n});\n// Create the auth provider component\nconst AuthProvider = (param)=>{\n    let { children } = param;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_3__.createSupabaseClient)();\n    // Initialize auth state\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeAuth = async ()=>{\n            try {\n                // Get the initial session\n                const { data: { session: initialSession } } = await supabase.auth.getSession();\n                setSession(initialSession);\n                var _initialSession_user;\n                setUser((_initialSession_user = initialSession === null || initialSession === void 0 ? void 0 : initialSession.user) !== null && _initialSession_user !== void 0 ? _initialSession_user : null);\n                // Set up auth state change listener\n                const { data: { subscription } } = await supabase.auth.onAuthStateChange((_event, newSession)=>{\n                    setSession(newSession);\n                    var _newSession_user;\n                    setUser((_newSession_user = newSession === null || newSession === void 0 ? void 0 : newSession.user) !== null && _newSession_user !== void 0 ? _newSession_user : null);\n                    setIsLoading(false);\n                });\n                // Cleanup subscription on unmount\n                return ()=>{\n                    subscription.unsubscribe();\n                };\n            } catch (err) {\n                console.error(\"Error initializing auth:\", err);\n                setError(\"Failed to initialize authentication\");\n                setIsLoading(false);\n            }\n        };\n        initializeAuth();\n    }, [\n        supabase\n    ]);\n    // Sign in with email and password\n    const signIn = async (email, password)=>{\n        try {\n            setError(null);\n            const { error } = await supabase.auth.signInWithPassword({\n                email,\n                password\n            });\n            if (error) {\n                setError(error.message);\n                return {\n                    error: error.message\n                };\n            }\n            return {\n                error: null\n            };\n        } catch (err) {\n            const errorMessage = err.message || \"An error occurred during sign in\";\n            setError(errorMessage);\n            return {\n                error: errorMessage\n            };\n        }\n    };\n    // Sign up with email and password\n    const signUp = async (email, password)=>{\n        try {\n            setError(null);\n            const { error } = await supabase.auth.signUp({\n                email,\n                password\n            });\n            if (error) {\n                setError(error.message);\n                return {\n                    error: error.message\n                };\n            }\n            return {\n                error: null\n            };\n        } catch (err) {\n            const errorMessage = err.message || \"An error occurred during sign up\";\n            setError(errorMessage);\n            return {\n                error: errorMessage\n            };\n        }\n    };\n    // Sign out\n    const signOut = async ()=>{\n        try {\n            await supabase.auth.signOut();\n            router.push(\"/login\");\n        } catch (err) {\n            console.error(\"Error signing out:\", err);\n            setError(err.message || \"An error occurred during sign out\");\n        }\n    };\n    // Reset password\n    const resetPassword = async (email)=>{\n        try {\n            setError(null);\n            const { error } = await supabase.auth.resetPasswordForEmail(email, {\n                redirectTo: \"\".concat(window.location.origin, \"/reset-password\")\n            });\n            if (error) {\n                setError(error.message);\n                return {\n                    error: error.message\n                };\n            }\n            return {\n                error: null\n            };\n        } catch (err) {\n            const errorMessage = err.message || \"An error occurred during password reset\";\n            setError(errorMessage);\n            return {\n                error: errorMessage\n            };\n        }\n    };\n    // Update user profile\n    const updateProfile = async (data)=>{\n        try {\n            setError(null);\n            const { error } = await supabase.auth.updateUser({\n                data\n            });\n            if (error) {\n                setError(error.message);\n                return {\n                    error: error.message\n                };\n            }\n            return {\n                error: null\n            };\n        } catch (err) {\n            const errorMessage = err.message || \"An error occurred while updating profile\";\n            setError(errorMessage);\n            return {\n                error: errorMessage\n            };\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            user,\n            session,\n            isLoading,\n            error,\n            signIn,\n            signUp,\n            signOut,\n            resetPassword,\n            updateProfile\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/context/AuthContext.tsx\",\n        lineNumber: 167,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AuthProvider, \"TiqfO+TleNWVtO5GNlqoH3hYj6c=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname\n    ];\n});\n_c = AuthProvider;\n// Custom hook to use the auth context\nconst useAuth = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n};\n_s1(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/context/AuthContext.tsx\n"));

/***/ })

});