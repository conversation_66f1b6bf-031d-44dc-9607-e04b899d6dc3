"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/account/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/account/page.tsx":
/*!********************************************!*\
  !*** ./src/app/dashboard/account/page.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ProfilePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Calendar_Camera_Edit3_Lock_Mail_MapPin_Phone_Save_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Camera,Edit3,Lock,Mail,MapPin,Phone,Save,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Camera_Edit3_Lock_Mail_MapPin_Phone_Save_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Camera,Edit3,Lock,Mail,MapPin,Phone,Save,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Camera_Edit3_Lock_Mail_MapPin_Phone_Save_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Camera,Edit3,Lock,Mail,MapPin,Phone,Save,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen-line.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Camera_Edit3_Lock_Mail_MapPin_Phone_Save_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Camera,Edit3,Lock,Mail,MapPin,Phone,Save,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Camera_Edit3_Lock_Mail_MapPin_Phone_Save_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Camera,Edit3,Lock,Mail,MapPin,Phone,Save,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Camera_Edit3_Lock_Mail_MapPin_Phone_Save_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Camera,Edit3,Lock,Mail,MapPin,Phone,Save,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Camera_Edit3_Lock_Mail_MapPin_Phone_Save_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Camera,Edit3,Lock,Mail,MapPin,Phone,Save,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Camera_Edit3_Lock_Mail_MapPin_Phone_Save_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Camera,Edit3,Lock,Mail,MapPin,Phone,Save,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Camera_Edit3_Lock_Mail_MapPin_Phone_Save_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Camera,Edit3,Lock,Mail,MapPin,Phone,Save,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Camera_Edit3_Lock_Mail_MapPin_Phone_Save_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Camera,Edit3,Lock,Mail,MapPin,Phone,Save,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _hooks_useAuthGuard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useAuthGuard */ \"(app-pages-browser)/./src/hooks/useAuthGuard.ts\");\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/context/AuthContext */ \"(app-pages-browser)/./src/context/AuthContext.tsx\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction ProfilePage() {\n    _s();\n    const [isEditing, setIsEditing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Ensure user is authenticated\n    const { user } = (0,_hooks_useAuthGuard__WEBPACK_IMPORTED_MODULE_2__.useRequireAuth)();\n    const { updateProfile } = (0,_context_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_4__.createSupabaseClient)();\n    const [profileData, setProfileData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        email: \"\",\n        phone: \"\",\n        location: \"\",\n        joinDate: \"\",\n        avatar: null\n    });\n    const [settings, setSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        emailNotifications: true,\n        pushNotifications: false,\n        twoFactorAuth: false\n    });\n    // Load user profile data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadProfile = async ()=>{\n            if (!user) {\n                setLoading(false);\n                return;\n            }\n            try {\n                var _user_email;\n                const { data: { session } } = await supabase.auth.getSession();\n                if (!session) {\n                    throw new Error(\"No active session\");\n                }\n                const response = await fetch(\"/api/user/profile\", {\n                    headers: {\n                        \"Authorization\": \"Bearer \".concat(session.access_token),\n                        \"Content-Type\": \"application/json\"\n                    }\n                });\n                if (!response.ok) {\n                    throw new Error(\"Failed to fetch profile\");\n                }\n                const { profile } = await response.json();\n                setProfileData({\n                    name: (profile === null || profile === void 0 ? void 0 : profile.full_name) || ((_user_email = user.email) === null || _user_email === void 0 ? void 0 : _user_email.split(\"@\")[0]) || \"\",\n                    email: user.email || \"\",\n                    phone: \"\",\n                    location: \"\",\n                    joinDate: new Date(user.created_at || \"\").toLocaleDateString(\"en-US\", {\n                        year: \"numeric\",\n                        month: \"long\"\n                    }),\n                    avatar: (profile === null || profile === void 0 ? void 0 : profile.avatar_url) || null\n                });\n            } catch (err) {\n                console.error(\"Error loading profile:\", err);\n                setError(err.message);\n            } finally{\n                setLoading(false);\n            }\n        };\n        loadProfile();\n    }, [\n        user,\n        supabase\n    ]);\n    const handleSave = async ()=>{\n        if (!user) return;\n        setSaving(true);\n        setError(null);\n        try {\n            // Update auth profile\n            const { error: authError } = await updateProfile({\n                full_name: profileData.name,\n                avatar_url: profileData.avatar\n            });\n            if (authError) {\n                throw new Error(authError);\n            }\n            // Update database profile\n            const { data: { session } } = await supabase.auth.getSession();\n            if (session) {\n                const response = await fetch(\"/api/user/profile\", {\n                    method: \"PUT\",\n                    headers: {\n                        \"Authorization\": \"Bearer \".concat(session.access_token),\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        full_name: profileData.name,\n                        avatar_url: profileData.avatar\n                    })\n                });\n                if (!response.ok) {\n                    throw new Error(\"Failed to update profile\");\n                }\n            }\n            setIsEditing(false);\n        } catch (err) {\n            console.error(\"Error saving profile:\", err);\n            setError(err.message);\n        } finally{\n            setSaving(false);\n        }\n    };\n    const handleInputChange = (field, value)=>{\n        setProfileData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    const handleSettingChange = (setting, value)=>{\n        setSettings((prev)=>({\n                ...prev,\n                [setting]: value\n            }));\n    };\n    // Show loading state\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-slate-600\",\n                        children: \"Loading profile...\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                lineNumber: 150,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n            lineNumber: 149,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b bg-white/80 backdrop-blur-sm border-slate-200/50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl px-6 py-4 mx-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold text-transparent bg-gradient-to-r from-slate-900 to-slate-600 bg-clip-text\",\n                                        children: \"Profile Settings\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-1 text-slate-600\",\n                                        children: \"Manage your account and preferences\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setIsEditing(false),\n                                            className: \"flex items-center gap-2 px-4 py-2 transition-all duration-200 rounded-lg text-slate-600 hover:text-slate-800 hover:bg-slate-100\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Camera_Edit3_Lock_Mail_MapPin_Phone_Save_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Cancel\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleSave,\n                                            className: \"flex items-center gap-2 px-4 py-2 text-white transition-all duration-200 rounded-lg shadow-lg bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 shadow-blue-500/25\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Camera_Edit3_Lock_Mail_MapPin_Phone_Save_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Save Changes\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setIsEditing(true),\n                                    className: \"flex items-center gap-2 px-4 py-2 text-white transition-all duration-200 rounded-lg shadow-lg bg-gradient-to-r from-slate-800 to-slate-600 hover:from-slate-900 hover:to-slate-700 shadow-slate-500/25\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Camera_Edit3_Lock_Mail_MapPin_Phone_Save_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"Edit Profile\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                    lineNumber: 162,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                lineNumber: 161,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl px-6 py-8 mx-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 gap-8 lg:grid-cols-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6 border shadow-xl bg-white/70 backdrop-blur-sm rounded-2xl shadow-slate-200/50 border-white/50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col items-center text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative group\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center w-20 h-20 text-2xl font-bold text-white rounded-full shadow-lg bg-gradient-to-br from-blue-500 to-indigo-600\",\n                                                    children: profileData.avatar ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: profileData.avatar,\n                                                        alt: \"Profile\",\n                                                        className: \"object-cover w-full h-full rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                        lineNumber: 211,\n                                                        columnNumber: 23\n                                                    }, this) : profileData.name.split(\" \").map((n)=>n[0]).join(\"\")\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                    lineNumber: 209,\n                                                    columnNumber: 19\n                                                }, this),\n                                                isEditing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"absolute flex items-center justify-center w-8 h-8 transition-colors bg-white border-2 border-blue-500 rounded-full shadow-lg -bottom-2 -right-2 hover:bg-blue-50\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Camera_Edit3_Lock_Mail_MapPin_Phone_Save_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        size: 14,\n                                                        className: \"text-blue-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                        lineNumber: 218,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                    lineNumber: 217,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"mt-3 text-lg font-semibold text-slate-800\",\n                                            children: profileData.name\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                            lineNumber: 222,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-slate-600\",\n                                            children: profileData.email\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-hidden border shadow-xl bg-white/70 backdrop-blur-sm rounded-2xl shadow-slate-200/50 border-white/50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-8 border-b border-slate-200/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"mb-2 text-xl font-semibold text-slate-800\",\n                                                        children: \"Personal Information\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                        lineNumber: 235,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-slate-600\",\n                                                        children: \"Update your personal details and profile information\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                        lineNumber: 236,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                lineNumber: 234,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 gap-6 md:grid-cols-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block mb-2 text-sm font-medium text-slate-700\",\n                                                                        children: \"Full Name\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                        lineNumber: 242,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Camera_Edit3_Lock_Mail_MapPin_Phone_Save_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                                className: \"absolute w-5 h-5 left-3 top-3 text-slate-400\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                                lineNumber: 244,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"text\",\n                                                                                value: profileData.name,\n                                                                                onChange: (e)=>handleInputChange(\"name\", e.target.value),\n                                                                                disabled: !isEditing,\n                                                                                className: \"w-full py-3 pl-10 pr-4 transition-all duration-200 border border-slate-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white/50 backdrop-blur-sm disabled:bg-slate-50/50 disabled:text-slate-500\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                                lineNumber: 245,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                        lineNumber: 243,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                lineNumber: 241,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block mb-2 text-sm font-medium text-slate-700\",\n                                                                        children: \"Email Address\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                        lineNumber: 256,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Camera_Edit3_Lock_Mail_MapPin_Phone_Save_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                                className: \"absolute w-5 h-5 left-3 top-3 text-slate-400\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                                lineNumber: 258,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"email\",\n                                                                                value: profileData.email,\n                                                                                onChange: (e)=>handleInputChange(\"email\", e.target.value),\n                                                                                disabled: !isEditing,\n                                                                                className: \"w-full py-3 pl-10 pr-4 transition-all duration-200 border border-slate-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white/50 backdrop-blur-sm disabled:bg-slate-50/50 disabled:text-slate-500\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                                lineNumber: 259,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                        lineNumber: 257,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                lineNumber: 255,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block mb-2 text-sm font-medium text-slate-700\",\n                                                                        children: \"Phone Number\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                        lineNumber: 270,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Camera_Edit3_Lock_Mail_MapPin_Phone_Save_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                className: \"absolute w-5 h-5 left-3 top-3 text-slate-400\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                                lineNumber: 272,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"tel\",\n                                                                                value: profileData.phone,\n                                                                                onChange: (e)=>handleInputChange(\"phone\", e.target.value),\n                                                                                disabled: !isEditing,\n                                                                                className: \"w-full py-3 pl-10 pr-4 transition-all duration-200 border border-slate-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white/50 backdrop-blur-sm disabled:bg-slate-50/50 disabled:text-slate-500\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                                lineNumber: 273,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                        lineNumber: 271,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                lineNumber: 269,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block mb-2 text-sm font-medium text-slate-700\",\n                                                                        children: \"Location\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                        lineNumber: 284,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Camera_Edit3_Lock_Mail_MapPin_Phone_Save_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                className: \"absolute w-5 h-5 left-3 top-3 text-slate-400\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                                lineNumber: 286,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"text\",\n                                                                                value: profileData.location,\n                                                                                onChange: (e)=>handleInputChange(\"location\", e.target.value),\n                                                                                disabled: !isEditing,\n                                                                                className: \"w-full py-3 pl-10 pr-4 transition-all duration-200 border border-slate-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white/50 backdrop-blur-sm disabled:bg-slate-50/50 disabled:text-slate-500\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                                lineNumber: 287,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                        lineNumber: 285,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                lineNumber: 283,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                        lineNumber: 240,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2 p-4 text-sm text-slate-600 bg-slate-50/50 rounded-xl\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Camera_Edit3_Lock_Mail_MapPin_Phone_Save_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                size: 16\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                lineNumber: 299,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \"Member since \",\n                                                                    profileData.joinDate\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                lineNumber: 300,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                        lineNumber: 298,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-8 border-b border-slate-200/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"mb-2 text-xl font-semibold text-slate-800\",\n                                                        children: \"Security Settings\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                        lineNumber: 308,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-slate-600\",\n                                                        children: \"Manage your account security and authentication\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                        lineNumber: 309,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-6 border border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                            className: \"font-semibold text-slate-800\",\n                                                                            children: \"Two-Factor Authentication\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                            lineNumber: 316,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"mt-1 text-sm text-slate-600\",\n                                                                            children: \"Add an extra layer of security to your account\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                            lineNumber: 317,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                    lineNumber: 315,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"checkbox\",\n                                                                            checked: settings.twoFactorAuth,\n                                                                            onChange: (e)=>handleSettingChange(\"twoFactorAuth\", e.target.checked),\n                                                                            className: \"sr-only\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                            lineNumber: 320,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>handleSettingChange(\"twoFactorAuth\", !settings.twoFactorAuth),\n                                                                            className: \"relative inline-flex h-6 w-11 items-center rounded-full transition-colors \".concat(settings.twoFactorAuth ? \"bg-blue-600\" : \"bg-slate-300\"),\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"inline-block h-4 w-4 transform rounded-full bg-white transition-transform \".concat(settings.twoFactorAuth ? \"translate-x-6\" : \"translate-x-1\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                                lineNumber: 332,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                            lineNumber: 326,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                    lineNumber: 319,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                            lineNumber: 314,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                        lineNumber: 313,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-6 border bg-white/50 rounded-xl border-slate-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"mb-4 font-semibold text-slate-800\",\n                                                                children: \"Change Password\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                lineNumber: 343,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block mb-2 text-sm font-medium text-slate-700\",\n                                                                                children: \"Current Password\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                                lineNumber: 346,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"relative\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Camera_Edit3_Lock_Mail_MapPin_Phone_Save_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                        className: \"absolute w-5 h-5 left-3 top-3 text-slate-400\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                                        lineNumber: 348,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                        type: \"password\",\n                                                                                        className: \"w-full py-3 pl-10 pr-4 transition-all duration-200 border border-slate-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                                                                                        placeholder: \"Enter current password\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                                        lineNumber: 349,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                                lineNumber: 347,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                        lineNumber: 345,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block mb-2 text-sm font-medium text-slate-700\",\n                                                                                children: \"New Password\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                                lineNumber: 357,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"relative\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Camera_Edit3_Lock_Mail_MapPin_Phone_Save_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                        className: \"absolute w-5 h-5 left-3 top-3 text-slate-400\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                                        lineNumber: 359,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                        type: \"password\",\n                                                                                        className: \"w-full py-3 pl-10 pr-4 transition-all duration-200 border border-slate-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                                                                                        placeholder: \"Enter new password\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                                        lineNumber: 360,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                                lineNumber: 358,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                        lineNumber: 356,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"px-4 py-2 text-white transition-all duration-200 rounded-lg bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700\",\n                                                                        children: \"Update Password\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                        lineNumber: 367,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                lineNumber: 344,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                        lineNumber: 342,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                lineNumber: 312,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-8 border-b border-slate-200/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"mb-2 text-xl font-semibold text-slate-800\",\n                                                        children: \"Notification Preferences\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                        lineNumber: 378,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-slate-600\",\n                                                        children: \"Choose how you want to be notified\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                        lineNumber: 379,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                lineNumber: 377,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-6 border bg-white/50 rounded-xl border-slate-200\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between mb-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                            className: \"font-semibold text-slate-800\",\n                                                                            children: \"Email Notifications\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                            lineNumber: 386,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"mt-1 text-sm text-slate-600\",\n                                                                            children: \"Receive notifications via email\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                            lineNumber: 387,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                    lineNumber: 385,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleSettingChange(\"emailNotifications\", !settings.emailNotifications),\n                                                                    className: \"relative inline-flex h-6 w-11 items-center rounded-full transition-colors \".concat(settings.emailNotifications ? \"bg-blue-600\" : \"bg-slate-300\"),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"inline-block h-4 w-4 transform rounded-full bg-white transition-transform \".concat(settings.emailNotifications ? \"translate-x-6\" : \"translate-x-1\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                        lineNumber: 395,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                    lineNumber: 389,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                            lineNumber: 384,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                        lineNumber: 383,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-6 border bg-white/50 rounded-xl border-slate-200\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between mb-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                            className: \"font-semibold text-slate-800\",\n                                                                            children: \"Push Notifications\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                            lineNumber: 407,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"mt-1 text-sm text-slate-600\",\n                                                                            children: \"Receive push notifications on your device\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                            lineNumber: 408,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                    lineNumber: 406,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleSettingChange(\"pushNotifications\", !settings.pushNotifications),\n                                                                    className: \"relative inline-flex h-6 w-11 items-center rounded-full transition-colors \".concat(settings.pushNotifications ? \"bg-blue-600\" : \"bg-slate-300\"),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"inline-block h-4 w-4 transform rounded-full bg-white transition-transform \".concat(settings.pushNotifications ? \"translate-x-6\" : \"translate-x-1\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                        lineNumber: 416,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                                    lineNumber: 410,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                            lineNumber: 405,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                        lineNumber: 404,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                lineNumber: 382,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                        lineNumber: 376,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"mb-2 text-xl font-semibold text-slate-800\",\n                                                        children: \"Account Management\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                        lineNumber: 430,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-slate-600\",\n                                                        children: \"Manage your account settings\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                        lineNumber: 431,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                lineNumber: 429,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-6 border border-red-200 bg-red-50 rounded-xl\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"mb-2 font-semibold text-red-800\",\n                                                        children: \"Delete Account\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                        lineNumber: 435,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mb-4 text-sm text-red-600\",\n                                                        children: \"Permanently delete your account and all associated data\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                        lineNumber: 436,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"px-4 py-2 text-white transition-colors bg-red-600 rounded-lg hover:bg-red-700\",\n                                                        children: \"Delete Account\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                        lineNumber: 437,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                                lineNumber: 434,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                        lineNumber: 428,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                    lineNumber: 203,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n                lineNumber: 202,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/account/page.tsx\",\n        lineNumber: 159,\n        columnNumber: 5\n    }, this);\n}\n_s(ProfilePage, \"cLaku2HbnfzpqaNU6hVYsxPq5QA=\", false, function() {\n    return [\n        _hooks_useAuthGuard__WEBPACK_IMPORTED_MODULE_2__.useRequireAuth,\n        _context_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = ProfilePage;\nvar _c;\n$RefreshReg$(_c, \"ProfilePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/account/page.tsx\n"));

/***/ })

});