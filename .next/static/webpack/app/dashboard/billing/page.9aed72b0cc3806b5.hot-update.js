"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/billing/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/billing/page.tsx":
/*!********************************************!*\
  !*** ./src/app/dashboard/billing/page.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ BillingPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n// Mock data for UI development (fallback)\nconst mockData = {\n    subscription: {\n        status: \"active\",\n        plan: \"Pro Plan\",\n        amount: 99.99,\n        currency: \"usd\",\n        nextBillingDate: \"2023-12-01\"\n    },\n    invoices: [\n        {\n            id: \"in_1\",\n            date: \"2023-11-01\",\n            amount: 99.99,\n            status: \"paid\",\n            url: \"#\"\n        },\n        {\n            id: \"in_2\",\n            date: \"2023-10-01\",\n            amount: 99.99,\n            status: \"paid\",\n            url: \"#\"\n        }\n    ],\n    paymentMethods: [\n        {\n            id: \"pm_1\",\n            type: \"card\",\n            last4: \"4242\",\n            brand: \"Visa\",\n            isDefault: true\n        },\n        {\n            id: \"pm_2\",\n            type: \"card\",\n            last4: \"1234\",\n            brand: \"Mastercard\",\n            isDefault: false\n        }\n    ]\n};\nfunction BillingPage() {\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"subscription\");\n    const [billingData, setBillingData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        async function fetchBillingData() {\n            try {\n                setLoading(true);\n                const response = await fetch(\"/api/stripe/billing-data\");\n                if (!response.ok) {\n                    throw new Error(\"Failed to fetch billing data.\");\n                }\n                const data = await response.json();\n                setBillingData(data);\n            } catch (err) {\n                setError(err.message);\n                // Fallback to mock data on error for UI development\n                setBillingData(mockData);\n            } finally{\n                setLoading(false);\n            }\n        }\n        fetchBillingData();\n    }, []);\n    const renderContent = ()=>{\n        if (loading) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: \"Loading...\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                lineNumber: 56,\n                columnNumber: 14\n            }, this);\n        }\n        if (error && !billingData) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-red-500\",\n                children: [\n                    \"Error: \",\n                    error\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                lineNumber: 60,\n                columnNumber: 14\n            }, this);\n        }\n        if (!billingData) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: \"No billing data found.\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                lineNumber: 64,\n                columnNumber: 14\n            }, this);\n        }\n        switch(activeTab){\n            case \"subscription\":\n                if (!billingData.subscription) {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"No active subscription found.\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 18\n                    }, this);\n                }\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold mb-4\",\n                            children: \"Current Subscription\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: [\n                                \"Status: \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"capitalize font-medium text-green-600\",\n                                    children: billingData.subscription.status\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 24\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: [\n                                \"Plan: \",\n                                billingData.subscription.plan\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: [\n                                \"Next Billing Date: \",\n                                new Date(billingData.subscription.nextBillingDate).toLocaleDateString()\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                    lineNumber: 74,\n                    columnNumber: 11\n                }, this);\n            case \"invoices\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold mb-4\",\n                            children: \"Billing History\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            children: billingData.invoices.map((invoice)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"border-b py-2 flex justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                new Date(invoice.date).toLocaleDateString(),\n                                                \" - $\",\n                                                invoice.amount.toFixed(2)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: invoice.url,\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"text-blue-500 hover:underline\",\n                                            children: \"View Invoice\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, invoice.id, true, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 11\n                }, this);\n            case \"paymentMethods\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold mb-4\",\n                            children: \"Payment Methods\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            children: billingData.paymentMethods.map((pm)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"border-b py-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            pm.brand,\n                                            \" ending in \",\n                                            pm.last4,\n                                            \" \",\n                                            pm.isDefault && \"(Default)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 19\n                                    }, this)\n                                }, pm.id, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                    lineNumber: 97,\n                    columnNumber: 11\n                }, this);\n            default:\n                return null;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6 bg-white rounded-lg shadow-md\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-2xl font-bold mb-6\",\n                children: \"Billing\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                lineNumber: 115,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"-mb-px flex space-x-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setActiveTab(\"subscription\"),\n                            className: \"whitespace-nowrap pb-4 px-1 border-b-2 font-medium text-sm \".concat(activeTab === \"subscription\" ? \"border-indigo-500 text-indigo-600\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"),\n                            children: \"Subscription\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setActiveTab(\"invoices\"),\n                            className: \"whitespace-nowrap pb-4 px-1 border-b-2 font-medium text-sm \".concat(activeTab === \"invoices\" ? \"border-indigo-500 text-indigo-600\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"),\n                            children: \"Invoices\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setActiveTab(\"paymentMethods\"),\n                            className: \"whitespace-nowrap pb-4 px-1 border-b-2 font-medium text-sm \".concat(activeTab === \"paymentMethods\" ? \"border-indigo-500 text-indigo-600\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"),\n                            children: \"Payment Methods\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                    lineNumber: 117,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                lineNumber: 116,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: renderContent()\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                lineNumber: 129,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n        lineNumber: 114,\n        columnNumber: 5\n    }, this);\n}\n_s(BillingPage, \"swJkh4/SVJKcreVHsdjVSGp2hHI=\");\n_c = BillingPage;\nvar _c;\n$RefreshReg$(_c, \"BillingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/billing/page.tsx\n"));

/***/ })

});