"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/context/AuthContext.tsx":
/*!*************************************!*\
  !*** ./src/context/AuthContext.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: function() { return /* binding */ AuthProvider; },\n/* harmony export */   useAuth: function() { return /* binding */ useAuth; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _hooks_useErrorHandler__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useErrorHandler */ \"(app-pages-browser)/./src/hooks/useErrorHandler.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n// Create the auth context with default values\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    user: null,\n    session: null,\n    isLoading: true,\n    error: null,\n    signIn: async ()=>({\n            error: null\n        }),\n    signUp: async ()=>({\n            error: null\n        }),\n    signOut: async ()=>{},\n    resetPassword: async ()=>({\n            error: null\n        }),\n    updateProfile: async ()=>({\n            error: null\n        })\n});\n// Create the auth provider component\nconst AuthProvider = (param)=>{\n    let { children } = param;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_3__.createSupabaseClient)();\n    const { handleAuthError, setErrorMessage, clearError, error } = (0,_hooks_useErrorHandler__WEBPACK_IMPORTED_MODULE_4__.useErrorHandler)();\n    // Initialize auth state\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeAuth = async ()=>{\n            try {\n                // Get the initial session\n                const { data: { session: initialSession } } = await supabase.auth.getSession();\n                setSession(initialSession);\n                var _initialSession_user;\n                setUser((_initialSession_user = initialSession === null || initialSession === void 0 ? void 0 : initialSession.user) !== null && _initialSession_user !== void 0 ? _initialSession_user : null);\n                // Set up auth state change listener\n                const { data: { subscription } } = await supabase.auth.onAuthStateChange((_event, newSession)=>{\n                    setSession(newSession);\n                    var _newSession_user;\n                    setUser((_newSession_user = newSession === null || newSession === void 0 ? void 0 : newSession.user) !== null && _newSession_user !== void 0 ? _newSession_user : null);\n                    setIsLoading(false);\n                });\n                // Cleanup subscription on unmount\n                return ()=>{\n                    subscription.unsubscribe();\n                };\n            } catch (err) {\n                console.error(\"Error initializing auth:\", err);\n                setError(\"Failed to initialize authentication\");\n                setIsLoading(false);\n            }\n        };\n        initializeAuth();\n    }, [\n        supabase\n    ]);\n    // Sign in with email and password\n    const signIn = async (email, password)=>{\n        try {\n            setError(null);\n            const { error } = await supabase.auth.signInWithPassword({\n                email,\n                password\n            });\n            if (error) {\n                setError(error.message);\n                return {\n                    error: error.message\n                };\n            }\n            return {\n                error: null\n            };\n        } catch (err) {\n            const errorMessage = err.message || \"An error occurred during sign in\";\n            setError(errorMessage);\n            return {\n                error: errorMessage\n            };\n        }\n    };\n    // Sign up with email and password\n    const signUp = async (email, password)=>{\n        try {\n            setError(null);\n            const { error } = await supabase.auth.signUp({\n                email,\n                password\n            });\n            if (error) {\n                setError(error.message);\n                return {\n                    error: error.message\n                };\n            }\n            return {\n                error: null\n            };\n        } catch (err) {\n            const errorMessage = err.message || \"An error occurred during sign up\";\n            setError(errorMessage);\n            return {\n                error: errorMessage\n            };\n        }\n    };\n    // Sign out\n    const signOut = async ()=>{\n        try {\n            await supabase.auth.signOut();\n            router.push(\"/login\");\n        } catch (err) {\n            console.error(\"Error signing out:\", err);\n            setError(err.message || \"An error occurred during sign out\");\n        }\n    };\n    // Reset password\n    const resetPassword = async (email)=>{\n        try {\n            setError(null);\n            const { error } = await supabase.auth.resetPasswordForEmail(email, {\n                redirectTo: \"\".concat(window.location.origin, \"/reset-password\")\n            });\n            if (error) {\n                setError(error.message);\n                return {\n                    error: error.message\n                };\n            }\n            return {\n                error: null\n            };\n        } catch (err) {\n            const errorMessage = err.message || \"An error occurred during password reset\";\n            setError(errorMessage);\n            return {\n                error: errorMessage\n            };\n        }\n    };\n    // Update user profile\n    const updateProfile = async (data)=>{\n        try {\n            setError(null);\n            const { error } = await supabase.auth.updateUser({\n                data\n            });\n            if (error) {\n                setError(error.message);\n                return {\n                    error: error.message\n                };\n            }\n            return {\n                error: null\n            };\n        } catch (err) {\n            const errorMessage = err.message || \"An error occurred while updating profile\";\n            setError(errorMessage);\n            return {\n                error: errorMessage\n            };\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            user,\n            session,\n            isLoading,\n            error,\n            signIn,\n            signUp,\n            signOut,\n            resetPassword,\n            updateProfile\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/context/AuthContext.tsx\",\n        lineNumber: 167,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AuthProvider, \"sF/Q3qOjCRscuGR5LhBP7zbrMRY=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        _hooks_useErrorHandler__WEBPACK_IMPORTED_MODULE_4__.useErrorHandler\n    ];\n});\n_c = AuthProvider;\n// Custom hook to use the auth context\nconst useAuth = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n};\n_s1(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/context/AuthContext.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/hooks/useErrorHandler.ts":
/*!**************************************!*\
  !*** ./src/hooks/useErrorHandler.ts ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useErrorHandler: function() { return /* binding */ useErrorHandler; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ useErrorHandler auto */ \n\nconst useErrorHandler = ()=>{\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    // Clear all states\n    const clearAll = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        setError(null);\n        setSuccess(null);\n        setIsLoading(false);\n    }, []);\n    // Clear error\n    const clearError = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        setError(null);\n    }, []);\n    // Clear success\n    const clearSuccess = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        setSuccess(null);\n    }, []);\n    // Set error with different types\n    const setErrorMessage = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function(message) {\n        let type = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"error\", code = arguments.length > 2 ? arguments[2] : void 0, details = arguments.length > 3 ? arguments[3] : void 0;\n        setError({\n            message,\n            type,\n            code,\n            details\n        });\n        setSuccess(null) // Clear success when error occurs\n        ;\n    }, []);\n    // Set success message\n    const setSuccessMessage = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function(message) {\n        let autoHide = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : true;\n        setSuccess({\n            message,\n            autoHide\n        });\n        setError(null) // Clear error when success occurs\n        ;\n        if (autoHide) {\n            setTimeout(()=>{\n                setSuccess(null);\n            }, 5000) // Auto-hide after 5 seconds\n            ;\n        }\n    }, []);\n    // Handle API errors with common patterns\n    const handleApiError = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function(error) {\n        let fallbackMessage = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"An unexpected error occurred\";\n        console.error(\"API Error:\", error);\n        if (error === null || error === void 0 ? void 0 : error.response) {\n            // HTTP error response\n            const status = error.response.status;\n            const data = error.response.data;\n            switch(status){\n                case 401:\n                    setErrorMessage(\"Your session has expired. Please log in again.\", \"error\", \"UNAUTHORIZED\");\n                    // Redirect to login after a short delay\n                    setTimeout(()=>{\n                        router.push(\"/login\");\n                    }, 2000);\n                    break;\n                case 403:\n                    setErrorMessage(\"You do not have permission to perform this action.\", \"error\", \"FORBIDDEN\");\n                    break;\n                case 404:\n                    setErrorMessage(\"The requested resource was not found.\", \"error\", \"NOT_FOUND\");\n                    break;\n                case 422:\n                    setErrorMessage((data === null || data === void 0 ? void 0 : data.message) || \"Please check your input and try again.\", \"error\", \"VALIDATION_ERROR\");\n                    break;\n                case 429:\n                    setErrorMessage(\"Too many requests. Please wait a moment and try again.\", \"error\", \"RATE_LIMIT\");\n                    break;\n                case 500:\n                    setErrorMessage(\"Server error. Please try again later.\", \"error\", \"SERVER_ERROR\");\n                    break;\n                default:\n                    setErrorMessage((data === null || data === void 0 ? void 0 : data.message) || fallbackMessage, \"error\", \"HTTP_\".concat(status));\n            }\n        } else if (error === null || error === void 0 ? void 0 : error.message) {\n            // Network or other errors\n            if (error.message.includes(\"fetch\")) {\n                setErrorMessage(\"Network error. Please check your connection and try again.\", \"error\", \"NETWORK_ERROR\");\n            } else {\n                setErrorMessage(error.message, \"error\", \"CLIENT_ERROR\");\n            }\n        } else {\n            setErrorMessage(fallbackMessage, \"error\", \"UNKNOWN_ERROR\");\n        }\n    }, [\n        setErrorMessage,\n        router\n    ]);\n    // Handle authentication errors specifically\n    const handleAuthError = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((error)=>{\n        console.error(\"Auth Error:\", error);\n        if (error === null || error === void 0 ? void 0 : error.message) {\n            if (error.message.includes(\"Invalid login credentials\")) {\n                setErrorMessage(\"Invalid email or password. Please try again.\", \"error\", \"INVALID_CREDENTIALS\");\n            } else if (error.message.includes(\"Email not confirmed\")) {\n                setErrorMessage(\"Please check your email and click the confirmation link.\", \"info\", \"EMAIL_NOT_CONFIRMED\");\n            } else if (error.message.includes(\"User already registered\")) {\n                setErrorMessage(\"An account with this email already exists. Please sign in instead.\", \"warning\", \"USER_EXISTS\");\n            } else if (error.message.includes(\"Password should be at least\")) {\n                setErrorMessage(\"Password must be at least 6 characters long.\", \"error\", \"WEAK_PASSWORD\");\n            } else {\n                setErrorMessage(error.message, \"error\", \"AUTH_ERROR\");\n            }\n        } else {\n            setErrorMessage(\"Authentication failed. Please try again.\", \"error\", \"AUTH_UNKNOWN\");\n        }\n    }, [\n        setErrorMessage\n    ]);\n    // Async operation wrapper with error handling\n    const withErrorHandling = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async function(operation) {\n        let { loadingMessage, successMessage, errorMessage = \"Operation failed\" } = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        try {\n            setIsLoading(true);\n            clearAll();\n            const result = await operation();\n            if (successMessage) {\n                setSuccessMessage(successMessage);\n            }\n            return result;\n        } catch (error) {\n            handleApiError(error, errorMessage);\n            return null;\n        } finally{\n            setIsLoading(false);\n        }\n    }, [\n        clearAll,\n        setSuccessMessage,\n        handleApiError\n    ]);\n    // Retry mechanism\n    const retry = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async function(operation) {\n        let maxRetries = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 3, delay = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 1000;\n        let lastError;\n        for(let attempt = 1; attempt <= maxRetries; attempt++){\n            try {\n                return await operation();\n            } catch (error) {\n                lastError = error;\n                if (attempt < maxRetries) {\n                    // Wait before retrying\n                    await new Promise((resolve)=>setTimeout(resolve, delay * attempt));\n                }\n            }\n        }\n        handleApiError(lastError, \"Operation failed after \".concat(maxRetries, \" attempts\"));\n        return null;\n    }, [\n        handleApiError\n    ]);\n    return {\n        // State\n        error,\n        success,\n        isLoading,\n        // Actions\n        clearAll,\n        clearError,\n        clearSuccess,\n        setErrorMessage,\n        setSuccessMessage,\n        setIsLoading,\n        // Handlers\n        handleApiError,\n        handleAuthError,\n        withErrorHandling,\n        retry,\n        // Computed\n        hasError: !!error,\n        hasSuccess: !!success\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useErrorHandler.ts\n"));

/***/ })

});