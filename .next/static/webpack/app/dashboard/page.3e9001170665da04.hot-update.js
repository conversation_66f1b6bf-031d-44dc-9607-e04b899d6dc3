"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useAuthGuard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useAuthGuard */ \"(app-pages-browser)/./src/hooks/useAuthGuard.ts\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _MapDomainModal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./MapDomainModal */ \"(app-pages-browser)/./src/app/dashboard/MapDomainModal.tsx\");\n/* harmony import */ var _AskDomainModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./AskDomainModal */ \"(app-pages-browser)/./src/app/dashboard/AskDomainModal.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst DashboardPage = ()=>{\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useSearchParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    const [sites, setSites] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [search, setSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isMapDomainOpen, setIsMapDomainOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAskDomainOpen, setIsAskDomainOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedSiteName, setSelectedSiteName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedSiteId, setSelectedSiteId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Ensure user is authenticated\n    const { user } = (0,_hooks_useAuthGuard__WEBPACK_IMPORTED_MODULE_2__.useRequireAuth)();\n    const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_3__.createSupabaseClient)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Trigger AskDomainModal if redirected from checkout\n        const postCheckout = searchParams.get(\"postCheckout\");\n        const siteIdFromParam = searchParams.get(\"siteId\");\n        if (postCheckout && siteIdFromParam) {\n            setSelectedSiteId(siteIdFromParam);\n            setIsAskDomainOpen(true);\n        }\n    }, [\n        searchParams\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchSites = async ()=>{\n            if (!user) {\n                setLoading(false);\n                return;\n            }\n            try {\n                // Get the session to include in API requests\n                const { data: { session } } = await supabase.auth.getSession();\n                if (!session) {\n                    throw new Error(\"No active session\");\n                }\n                // Fetch sites using the authenticated API\n                const response = await fetch(\"/api/user/sites\", {\n                    headers: {\n                        \"Authorization\": \"Bearer \".concat(session.access_token),\n                        \"Content-Type\": \"application/json\"\n                    }\n                });\n                if (!response.ok) {\n                    throw new Error(\"Failed to fetch sites\");\n                }\n                const { sites } = await response.json();\n                setSites(sites);\n            } catch (err) {\n                console.error(\"Error fetching sites:\", err);\n                setError(err instanceof Error ? err.message : \"An unknown error occurred\");\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchSites();\n    }, [\n        user,\n        supabase\n    ]);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"py-8 text-center\",\n            children: \"Loading sites...\"\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n            lineNumber: 84,\n            columnNumber: 12\n        }, undefined);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"py-8 text-center text-red-500\",\n            children: [\n                \"Error: \",\n                error\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n            lineNumber: 88,\n            columnNumber: 12\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full p-8 bg-gray-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between p-6 mb-6 bg-white rounded-lg shadow-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-semibold text-gray-800\",\n                        children: \"Websites\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    placeholder: \"Search\",\n                                    className: \"py-2 pl-10 pr-4 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                    value: search,\n                                    onChange: (e)=>setSearch(e.target.value)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"absolute w-5 h-5 text-gray-400 transform -translate-y-1/2 left-3 top-1/2\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: \"2\",\n                                        d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                lineNumber: 94,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-hidden bg-white rounded-lg shadow-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                    className: \"min-w-full divide-y divide-gray-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                            className: \"bg-gray-50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        scope: \"col\",\n                                        className: \"px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase\",\n                                        children: \"Site Name\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        scope: \"col\",\n                                        className: \"px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase\",\n                                        children: \"Actions\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        scope: \"col\",\n                                        className: \"px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase\",\n                                        children: \"Expiry\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                            className: \"bg-white divide-y divide-gray-200\",\n                            children: (search.trim() ? sites.filter((site)=>site.site_name.toLowerCase().includes(search.toLowerCase())) : sites).map((site)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"flex items-center px-6 py-4 text-sm font-medium text-gray-900 whitespace-nowrap\",\n                                            children: [\n                                                site.site_name,\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"ml-2 text-gray-400 hover:text-gray-600\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        className: \"w-4 h-4\",\n                                                        fill: \"none\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        stroke: \"currentColor\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 155,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 154,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4 text-sm font-medium text-right whitespace-nowrap\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"text-blue-500 hover:text-blue-700\",\n                                                        title: \"Map Domain\",\n                                                        onClick: ()=>{\n                                                            setSelectedSiteName(site.site_name);\n                                                            setIsMapDomainOpen(true);\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            className: \"w-5 h-5\",\n                                                            fill: \"none\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            stroke: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 168,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 167,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 162,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    site.expiry_status === \"Temporary\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"text-green-500 hover:text-green-700\",\n                                                        title: \"Choose Plan\",\n                                                        onClick: ()=>{\n                                                            window.location.href = \"/payments?siteId=\".concat(site.id);\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            className: \"w-5 h-5\",\n                                                            fill: \"none\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            stroke: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M13 10V3L4 14h7v7l9-11h-7z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 179,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 178,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 173,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    site.expiry_status === \"Permanent\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"text-purple-500 hover:text-purple-700\",\n                                                        title: \"Change Plan\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            className: \"w-5 h-5\",\n                                                            fill: \"none\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            stroke: \"currentColor\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                                                                    x: \"2\",\n                                                                    y: \"7\",\n                                                                    width: \"20\",\n                                                                    height: \"10\",\n                                                                    rx: \"2\",\n                                                                    ry: \"2\",\n                                                                    stroke: \"currentColor\",\n                                                                    strokeWidth: \"2\",\n                                                                    fill: \"none\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                                    lineNumber: 190,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M2 11h20\",\n                                                                    stroke: \"currentColor\",\n                                                                    strokeWidth: \"2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                                    lineNumber: 191,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                    cx: \"7\",\n                                                                    cy: \"15\",\n                                                                    r: \"1\",\n                                                                    fill: \"currentColor\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                                    lineNumber: 192,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                    cx: \"11\",\n                                                                    cy: \"15\",\n                                                                    r: \"1\",\n                                                                    fill: \"currentColor\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                                    lineNumber: 193,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 189,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 185,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4 text-sm whitespace-nowrap\",\n                                            children: site.expiry_status === \"Temporary\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800\",\n                                                title: (()=>{\n                                                    if (!site.expiry_time) return \"No expiry time set\";\n                                                    const expiry = new Date(site.expiry_time);\n                                                    const now = new Date();\n                                                    const diffMs = expiry.getTime() - now.getTime();\n                                                    if (diffMs <= 0) return \"Expired\";\n                                                    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));\n                                                    const diffHours = Math.floor(diffMs % (1000 * 60 * 60 * 24) / (1000 * 60 * 60));\n                                                    return \"\".concat(diffDays, \" days \").concat(diffHours, \" hours left (expires at \").concat(expiry.toLocaleString(), \")\");\n                                                })(),\n                                                children: site.expiry_status\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 21\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800\",\n                                                children: site.expiry_status\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                            lineNumber: 199,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, site.id, true, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                    lineNumber: 125,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 text-sm text-gray-600 bg-white border-t border-gray-200 rounded-b-lg\",\n                children: [\n                    sites.length,\n                    \" Sites\"\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                lineNumber: 231,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MapDomainModal__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                isOpen: isMapDomainOpen,\n                onClose: ()=>setIsMapDomainOpen(false),\n                siteName: selectedSiteName\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                lineNumber: 234,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AskDomainModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                isOpen: isAskDomainOpen,\n                onYes: ()=>{\n                    // open map domain modal\n                    const site = sites.find((s)=>s.id === selectedSiteId);\n                    if (site) {\n                        setSelectedSiteName(site.site_name);\n                        setIsMapDomainOpen(true);\n                    }\n                    setIsAskDomainOpen(false);\n                },\n                onNo: ()=>{\n                    router.push(\"/dashboard/domain\");\n                    setIsAskDomainOpen(false);\n                }\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                lineNumber: 240,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n        lineNumber: 92,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DashboardPage, \"txg1RpKssKV9vKdJtc8y0c5usK0=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useSearchParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter,\n        _hooks_useAuthGuard__WEBPACK_IMPORTED_MODULE_2__.useRequireAuth\n    ];\n});\n_c = DashboardPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DashboardPage);\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ })

});