'use client'

import { createContext, useContext, useEffect, useState, ReactNode } from 'react'
import { Session, User } from '@supabase/supabase-js'
import { useRouter, usePathname } from 'next/navigation'
import { createSupabaseClient } from '@/lib/supabase'

// Define the shape of our auth context
type AuthContextType = {
  user: User | null
  session: Session | null
  isLoading: boolean
  error: string | null
  signIn: (email: string, password: string) => Promise<{ error: string | null }>
  signUp: (email: string, password: string) => Promise<{ error: string | null }>
  signOut: () => Promise<void>
  resetPassword: (email: string) => Promise<{ error: string | null }>
  updateProfile: (data: { full_name?: string; avatar_url?: string }) => Promise<{ error: string | null }>
}

// Create the auth context with default values
const AuthContext = createContext<AuthContextType>({
  user: null,
  session: null,
  isLoading: true,
  error: null,
  signIn: async () => ({ error: null }),
  signUp: async () => ({ error: null }),
  signOut: async () => {},
  resetPassword: async () => ({ error: null }),
  updateProfile: async () => ({ error: null }),
})

// Auth provider props
interface AuthProviderProps {
  children: ReactNode
}

// Create the auth provider component
export const AuthProvider = ({ children }: AuthProviderProps) => {
  const [user, setUser] = useState<User | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  const router = useRouter()
  const pathname = usePathname()
  const supabase = createSupabaseClient()

  // Initialize auth state
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        // Get the initial session
        const { data: { session: initialSession } } = await supabase.auth.getSession()
        setSession(initialSession)
        setUser(initialSession?.user ?? null)

        // Set up auth state change listener
        const { data: { subscription } } = await supabase.auth.onAuthStateChange(
          (_event, newSession) => {
            setSession(newSession)
            setUser(newSession?.user ?? null)
            setIsLoading(false)
          }
        )

        // Cleanup subscription on unmount
        return () => {
          subscription.unsubscribe()
        }
      } catch (err) {
        console.error('Error initializing auth:', err)
        setError('Failed to initialize authentication')
        setIsLoading(false)
      }
    }

    initializeAuth()
  }, [supabase])

  // Sign in with email and password
  const signIn = async (email: string, password: string) => {
    try {
      setError(null)
      const { error } = await supabase.auth.signInWithPassword({ email, password })
      if (error) {
        setError(error.message)
        return { error: error.message }
      }
      return { error: null }
    } catch (err: any) {
      const errorMessage = err.message || 'An error occurred during sign in'
      setError(errorMessage)
      return { error: errorMessage }
    }
  }

  // Sign up with email and password
  const signUp = async (email: string, password: string) => {
    try {
      setError(null)
      const { error } = await supabase.auth.signUp({ email, password })
      if (error) {
        setError(error.message)
        return { error: error.message }
      }
      return { error: null }
    } catch (err: any) {
      const errorMessage = err.message || 'An error occurred during sign up'
      setError(errorMessage)
      return { error: errorMessage }
    }
  }

  // Sign out
  const signOut = async () => {
    try {
      await supabase.auth.signOut()
      router.push('/login')
    } catch (err: any) {
      console.error('Error signing out:', err)
      setError(err.message || 'An error occurred during sign out')
    }
  }

  // Reset password
  const resetPassword = async (email: string) => {
    try {
      setError(null)
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/reset-password`,
      })
      if (error) {
        setError(error.message)
        return { error: error.message }
      }
      return { error: null }
    } catch (err: any) {
      const errorMessage = err.message || 'An error occurred during password reset'
      setError(errorMessage)
      return { error: errorMessage }
    }
  }

  // Update user profile
  const updateProfile = async (data: { full_name?: string; avatar_url?: string }) => {
    try {
      setError(null)
      const { error } = await supabase.auth.updateUser({
        data,
      })
      if (error) {
        setError(error.message)
        return { error: error.message }
      }
      return { error: null }
    } catch (err: any) {
      const errorMessage = err.message || 'An error occurred while updating profile'
      setError(errorMessage)
      return { error: errorMessage }
    }
  }

  return (
    <AuthContext.Provider
      value={{
        user,
        session,
        isLoading,
        error,
        signIn,
        signUp,
        signOut,
        resetPassword,
        updateProfile,
      }}
    >
      {children}
    </AuthContext.Provider>
  )
}

// Custom hook to use the auth context
export const useAuth = () => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
