import { NextRequest } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { Database } from './supabase'

// Server-side Supabase client for API routes
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

export const supabaseAdmin = createClient<Database>(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

// Extract JWT token from request headers
export const getTokenFromRequest = (request: NextRequest): string | null => {
  const authHeader = request.headers.get('authorization')
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7)
  }
  return null
}

// Validate JWT token and get user
export const validateAuthToken = async (token: string) => {
  try {
    const { data: { user }, error } = await supabaseAdmin.auth.getUser(token)
    
    if (error || !user) {
      return { user: null, error: error?.message || 'Invalid token' }
    }

    return { user, error: null }
  } catch (error: any) {
    return { user: null, error: error.message || 'Token validation failed' }
  }
}

// Get authenticated user from request
export const getAuthenticatedUser = async (request: NextRequest) => {
  const token = getTokenFromRequest(request)
  
  if (!token) {
    return { user: null, error: 'No authorization token provided' }
  }

  return await validateAuthToken(token)
}

// Middleware helper to check if user is authenticated
export const requireAuth = async (request: NextRequest) => {
  const { user, error } = await getAuthenticatedUser(request)
  
  if (!user) {
    return {
      authenticated: false,
      user: null,
      error: error || 'Authentication required',
    }
  }

  return {
    authenticated: true,
    user,
    error: null,
  }
}

// Get user profile with additional data
export const getUserProfile = async (userId: string) => {
  try {
    const { data: profile, error } = await supabaseAdmin
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single()

    if (error) {
      return { profile: null, error: error.message }
    }

    return { profile, error: null }
  } catch (error: any) {
    return { profile: null, error: error.message || 'Failed to fetch user profile' }
  }
}

// Create or update user profile
export const upsertUserProfile = async (userId: string, profileData: {
  email: string
  full_name?: string
  avatar_url?: string
  stripe_customer_id?: string
}) => {
  try {
    const { data: profile, error } = await supabaseAdmin
      .from('profiles')
      .upsert({
        id: userId,
        ...profileData,
        updated_at: new Date().toISOString(),
      })
      .select()
      .single()

    if (error) {
      return { profile: null, error: error.message }
    }

    return { profile, error: null }
  } catch (error: any) {
    return { profile: null, error: error.message || 'Failed to update user profile' }
  }
}

// Get user's sites
export const getUserSites = async (userId: string) => {
  try {
    const { data: sites, error } = await supabaseAdmin
      .from('sites')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })

    if (error) {
      return { sites: [], error: error.message }
    }

    return { sites: sites || [], error: null }
  } catch (error: any) {
    return { sites: [], error: error.message || 'Failed to fetch user sites' }
  }
}

// Create a new site for user
export const createUserSite = async (userId: string, siteData: {
  site_name: string
  expiry_status?: 'Permanent' | 'Temporary'
  expiry_time?: string | null
}) => {
  try {
    const { data: site, error } = await supabaseAdmin
      .from('sites')
      .insert({
        user_id: userId,
        ...siteData,
      })
      .select()
      .single()

    if (error) {
      return { site: null, error: error.message }
    }

    return { site, error: null }
  } catch (error: any) {
    return { site: null, error: error.message || 'Failed to create site' }
  }
}

// Update user's site
export const updateUserSite = async (userId: string, siteId: string, updates: {
  site_name?: string
  expiry_status?: 'Permanent' | 'Temporary'
  expiry_time?: string | null
}) => {
  try {
    const { data: site, error } = await supabaseAdmin
      .from('sites')
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq('id', siteId)
      .eq('user_id', userId) // Ensure user owns the site
      .select()
      .single()

    if (error) {
      return { site: null, error: error.message }
    }

    return { site, error: null }
  } catch (error: any) {
    return { site: null, error: error.message || 'Failed to update site' }
  }
}

// Delete user's site
export const deleteUserSite = async (userId: string, siteId: string) => {
  try {
    const { error } = await supabaseAdmin
      .from('sites')
      .delete()
      .eq('id', siteId)
      .eq('user_id', userId) // Ensure user owns the site

    if (error) {
      return { success: false, error: error.message }
    }

    return { success: true, error: null }
  } catch (error: any) {
    return { success: false, error: error.message || 'Failed to delete site' }
  }
}
