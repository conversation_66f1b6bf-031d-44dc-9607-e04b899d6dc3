import { NextRequest, NextResponse } from 'next/server'
import { getAuthenticatedUser } from './auth-utils'

// Type for authenticated API handler
export type AuthenticatedHandler = (
  req: NextRequest,
  user: any,
  context?: any
) => Promise<NextResponse>

// Wrapper function to create authenticated API routes
export function withAuth(handler: Authenticated<PERSON>andler) {
  return async (req: NextRequest, context?: any) => {
    try {
      // Get authenticated user
      const { user, error } = await getAuthenticatedUser(req)

      if (!user) {
        return NextResponse.json(
          { error: error || 'Authentication required' },
          { status: 401 }
        )
      }

      // Call the actual handler with the authenticated user
      return await handler(req, user, context)
    } catch (error: any) {
      console.error('API authentication error:', error)
      return NextResponse.json(
        { error: 'Internal server error' },
        { status: 500 }
      )
    }
  }
}

// Helper to get authorization header from request
export function getAuthHeader(req: NextRequest): string | null {
  return req.headers.get('authorization')
}

// Helper to create error responses
export function createErrorResponse(message: string, status: number = 400) {
  return NextResponse.json({ error: message }, { status })
}

// Helper to create success responses
export function createSuccessResponse(data: any, status: number = 200) {
  return NextResponse.json(data, { status })
}

// Middleware to add CORS headers
export function withCors(response: NextResponse): NextResponse {
  response.headers.set('Access-Control-Allow-Origin', '*')
  response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
  response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization')
  return response
}

// Helper to validate request method
export function validateMethod(req: NextRequest, allowedMethods: string[]): boolean {
  return allowedMethods.includes(req.method)
}

// Helper to parse JSON body safely
export async function parseJsonBody(req: NextRequest): Promise<{ data: any; error: string | null }> {
  try {
    const data = await req.json()
    return { data, error: null }
  } catch (error: any) {
    return { data: null, error: 'Invalid JSON body' }
  }
}

// Helper to validate required fields
export function validateRequiredFields(data: any, requiredFields: string[]): string | null {
  for (const field of requiredFields) {
    if (!data[field]) {
      return `Missing required field: ${field}`
    }
  }
  return null
}

// Combined helper for authenticated API routes with common validations
export function createAuthenticatedRoute(options: {
  allowedMethods: string[]
  requiredFields?: string[]
  handler: AuthenticatedHandler
}) {
  return withAuth(async (req: NextRequest, user: any, context?: any) => {
    // Validate method
    if (!validateMethod(req, options.allowedMethods)) {
      return createErrorResponse(`Method ${req.method} not allowed`, 405)
    }

    // Parse and validate body for non-GET requests
    if (req.method !== 'GET' && options.requiredFields) {
      const { data, error } = await parseJsonBody(req)
      if (error) {
        return createErrorResponse(error, 400)
      }

      const validationError = validateRequiredFields(data, options.requiredFields)
      if (validationError) {
        return createErrorResponse(validationError, 400)
      }

      // Add parsed data to request for handler
      ;(req as any).parsedBody = data
    }

    return await options.handler(req, user, context)
  })
}
