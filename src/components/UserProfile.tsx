'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/context/AuthContext'
import { createSupabaseClient } from '@/lib/supabase'
import { User, Mail, Calendar, Shield } from 'lucide-react'

interface UserProfileData {
  id: string
  email: string
  full_name: string | null
  avatar_url: string | null
  stripe_customer_id: string | null
  created_at: string
  updated_at: string
}

interface UserProfileProps {
  showDetails?: boolean
  className?: string
}

export default function UserProfile({ showDetails = true, className = '' }: UserProfileProps) {
  const { user } = useAuth()
  const [profile, setProfile] = useState<UserProfileData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const supabase = createSupabaseClient()

  useEffect(() => {
    const fetchProfile = async () => {
      if (!user) {
        setLoading(false)
        return
      }

      try {
        const { data: { session } } = await supabase.auth.getSession()
        
        if (!session) {
          throw new Error('No active session')
        }

        const response = await fetch('/api/user/profile', {
          headers: {
            'Authorization': `Bearer ${session.access_token}`,
            'Content-Type': 'application/json',
          },
        })

        if (!response.ok) {
          throw new Error('Failed to fetch profile')
        }

        const { profile } = await response.json()
        setProfile(profile)
      } catch (err: any) {
        console.error('Error fetching profile:', err)
        setError(err.message)
      } finally {
        setLoading(false)
      }
    }

    fetchProfile()
  }, [user, supabase])

  if (loading) {
    return (
      <div className={`animate-pulse ${className}`}>
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gray-300 rounded-full"></div>
          <div className="space-y-2">
            <div className="h-4 bg-gray-300 rounded w-24"></div>
            <div className="h-3 bg-gray-300 rounded w-32"></div>
          </div>
        </div>
      </div>
    )
  }

  if (error || !user) {
    return (
      <div className={`text-red-500 text-sm ${className}`}>
        {error || 'User not found'}
      </div>
    )
  }

  const displayName = profile?.full_name || user.email?.split('@')[0] || 'User'
  const joinDate = new Date(user.created_at || '').toLocaleDateString()

  return (
    <div className={`${className}`}>
      <div className="flex items-center space-x-3">
        {/* Avatar */}
        <div className="flex-shrink-0">
          {profile?.avatar_url ? (
            <img
              src={profile.avatar_url}
              alt={displayName}
              className="w-10 h-10 rounded-full object-cover"
            />
          ) : (
            <div className="w-10 h-10 bg-indigo-100 rounded-full flex items-center justify-center">
              <User className="w-6 h-6 text-indigo-600" />
            </div>
          )}
        </div>

        {/* User Info */}
        <div className="flex-1 min-w-0">
          <div className="text-sm font-medium text-gray-900 truncate">
            {displayName}
          </div>
          <div className="text-sm text-gray-500 truncate">
            {user.email}
          </div>
        </div>
      </div>

      {/* Detailed Information */}
      {showDetails && (
        <div className="mt-4 space-y-3">
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <Mail className="w-4 h-4" />
            <span>{user.email}</span>
          </div>
          
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <Calendar className="w-4 h-4" />
            <span>Joined {joinDate}</span>
          </div>
          
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <Shield className="w-4 h-4" />
            <span className="capitalize">
              {user.email_confirmed_at ? 'Verified' : 'Unverified'} Account
            </span>
          </div>

          {profile?.stripe_customer_id && (
            <div className="text-xs text-gray-500">
              Billing ID: {profile.stripe_customer_id}
            </div>
          )}
        </div>
      )}
    </div>
  )
}

// Compact version for headers/navigation
export function UserProfileCompact({ className = '' }: { className?: string }) {
  return <UserProfile showDetails={false} className={className} />
}

// Full version for profile pages
export function UserProfileFull({ className = '' }: { className?: string }) {
  return <UserProfile showDetails={true} className={className} />
}
