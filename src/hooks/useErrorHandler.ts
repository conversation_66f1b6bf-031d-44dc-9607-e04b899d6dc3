'use client'

import { useState, useCallback } from 'react'
import { useRouter } from 'next/navigation'

export interface ErrorState {
  message: string | null
  type: 'error' | 'warning' | 'info'
  code?: string
  details?: any
}

export interface SuccessState {
  message: string | null
  autoHide?: boolean
}

export const useErrorHandler = () => {
  const [error, setError] = useState<ErrorState | null>(null)
  const [success, setSuccess] = useState<SuccessState | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()

  // Clear all states
  const clearAll = useCallback(() => {
    setError(null)
    setSuccess(null)
    setIsLoading(false)
  }, [])

  // Clear error
  const clearError = useCallback(() => {
    setError(null)
  }, [])

  // Clear success
  const clearSuccess = useCallback(() => {
    setSuccess(null)
  }, [])

  // Set error with different types
  const setErrorMessage = useCallback((
    message: string, 
    type: 'error' | 'warning' | 'info' = 'error',
    code?: string,
    details?: any
  ) => {
    setError({ message, type, code, details })
    setSuccess(null) // Clear success when error occurs
  }, [])

  // Set success message
  const setSuccessMessage = useCallback((message: string, autoHide = true) => {
    setSuccess({ message, autoHide })
    setError(null) // Clear error when success occurs
    
    if (autoHide) {
      setTimeout(() => {
        setSuccess(null)
      }, 5000) // Auto-hide after 5 seconds
    }
  }, [])

  // Handle API errors with common patterns
  const handleApiError = useCallback((error: any, fallbackMessage = 'An unexpected error occurred') => {
    console.error('API Error:', error)
    
    if (error?.response) {
      // HTTP error response
      const status = error.response.status
      const data = error.response.data
      
      switch (status) {
        case 401:
          setErrorMessage('Your session has expired. Please log in again.', 'error', 'UNAUTHORIZED')
          // Redirect to login after a short delay
          setTimeout(() => {
            router.push('/login')
          }, 2000)
          break
        case 403:
          setErrorMessage('You do not have permission to perform this action.', 'error', 'FORBIDDEN')
          break
        case 404:
          setErrorMessage('The requested resource was not found.', 'error', 'NOT_FOUND')
          break
        case 422:
          setErrorMessage(data?.message || 'Please check your input and try again.', 'error', 'VALIDATION_ERROR')
          break
        case 429:
          setErrorMessage('Too many requests. Please wait a moment and try again.', 'error', 'RATE_LIMIT')
          break
        case 500:
          setErrorMessage('Server error. Please try again later.', 'error', 'SERVER_ERROR')
          break
        default:
          setErrorMessage(data?.message || fallbackMessage, 'error', `HTTP_${status}`)
      }
    } else if (error?.message) {
      // Network or other errors
      if (error.message.includes('fetch')) {
        setErrorMessage('Network error. Please check your connection and try again.', 'error', 'NETWORK_ERROR')
      } else {
        setErrorMessage(error.message, 'error', 'CLIENT_ERROR')
      }
    } else {
      setErrorMessage(fallbackMessage, 'error', 'UNKNOWN_ERROR')
    }
  }, [setErrorMessage, router])

  // Handle authentication errors specifically
  const handleAuthError = useCallback((error: any) => {
    console.error('Auth Error:', error)
    
    if (error?.message) {
      if (error.message.includes('Invalid login credentials')) {
        setErrorMessage('Invalid email or password. Please try again.', 'error', 'INVALID_CREDENTIALS')
      } else if (error.message.includes('Email not confirmed')) {
        setErrorMessage('Please check your email and click the confirmation link.', 'info', 'EMAIL_NOT_CONFIRMED')
      } else if (error.message.includes('User already registered')) {
        setErrorMessage('An account with this email already exists. Please sign in instead.', 'warning', 'USER_EXISTS')
      } else if (error.message.includes('Password should be at least')) {
        setErrorMessage('Password must be at least 6 characters long.', 'error', 'WEAK_PASSWORD')
      } else {
        setErrorMessage(error.message, 'error', 'AUTH_ERROR')
      }
    } else {
      setErrorMessage('Authentication failed. Please try again.', 'error', 'AUTH_UNKNOWN')
    }
  }, [setErrorMessage])

  // Async operation wrapper with error handling
  const withErrorHandling = useCallback(async <T>(
    operation: () => Promise<T>,
    {
      loadingMessage,
      successMessage,
      errorMessage = 'Operation failed'
    }: {
      loadingMessage?: string
      successMessage?: string
      errorMessage?: string
    } = {}
  ): Promise<T | null> => {
    try {
      setIsLoading(true)
      clearAll()
      
      const result = await operation()
      
      if (successMessage) {
        setSuccessMessage(successMessage)
      }
      
      return result
    } catch (error: any) {
      handleApiError(error, errorMessage)
      return null
    } finally {
      setIsLoading(false)
    }
  }, [clearAll, setSuccessMessage, handleApiError])

  // Retry mechanism
  const retry = useCallback(async <T>(
    operation: () => Promise<T>,
    maxRetries = 3,
    delay = 1000
  ): Promise<T | null> => {
    let lastError: any
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation()
      } catch (error: any) {
        lastError = error
        
        if (attempt < maxRetries) {
          // Wait before retrying
          await new Promise(resolve => setTimeout(resolve, delay * attempt))
        }
      }
    }
    
    handleApiError(lastError, `Operation failed after ${maxRetries} attempts`)
    return null
  }, [handleApiError])

  return {
    // State
    error,
    success,
    isLoading,
    
    // Actions
    clearAll,
    clearError,
    clearSuccess,
    setErrorMessage,
    setSuccessMessage,
    setIsLoading,
    
    // Handlers
    handleApiError,
    handleAuthError,
    withErrorHandling,
    retry,
    
    // Computed
    hasError: !!error,
    hasSuccess: !!success,
  }
}
