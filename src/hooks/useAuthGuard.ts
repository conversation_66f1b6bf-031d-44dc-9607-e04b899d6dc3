'use client'

import { useEffect } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { useAuth } from '@/context/AuthContext'

// Define public routes that don't require authentication
const PUBLIC_ROUTES = [
  '/',
  '/login',
  '/signup',
  '/reset-password',
  '/verify-email',
]

// Define routes that should redirect authenticated users (like login/signup)
const AUTH_REDIRECT_ROUTES = [
  '/login',
  '/signup',
]

interface UseAuthGuardOptions {
  redirectTo?: string
  requireAuth?: boolean
  redirectIfAuthenticated?: boolean
}

export const useAuthGuard = (options: UseAuthGuardOptions = {}) => {
  const {
    redirectTo = '/login',
    requireAuth = true,
    redirectIfAuthenticated = false,
  } = options

  const { user, isLoading } = useAuth()
  const router = useRouter()
  const pathname = usePathname()

  useEffect(() => {
    // Don't do anything while loading
    if (isLoading) return

    // If user is authenticated and should be redirected (e.g., on login page)
    if (user && redirectIfAuthenticated) {
      router.push('/dashboard')
      return
    }

    // If authentication is required and user is not authenticated
    if (requireAuth && !user) {
      // Check if current route is public
      const isPublicRoute = PUBLIC_ROUTES.some(route => 
        pathname === route || pathname.startsWith(route + '/')
      )

      if (!isPublicRoute) {
        router.push(redirectTo)
        return
      }
    }
  }, [user, isLoading, pathname, router, redirectTo, requireAuth, redirectIfAuthenticated])

  return {
    user,
    isLoading,
    isAuthenticated: !!user,
    isPublicRoute: PUBLIC_ROUTES.includes(pathname),
    shouldRedirect: requireAuth && !user && !PUBLIC_ROUTES.includes(pathname),
  }
}

// Hook specifically for protected routes
export const useRequireAuth = (redirectTo = '/login') => {
  return useAuthGuard({ requireAuth: true, redirectTo })
}

// Hook specifically for auth pages (login/signup) that should redirect if already authenticated
export const useRedirectIfAuthenticated = (redirectTo = '/dashboard') => {
  const pathname = usePathname()
  const shouldRedirect = AUTH_REDIRECT_ROUTES.includes(pathname)
  
  return useAuthGuard({ 
    requireAuth: false, 
    redirectIfAuthenticated: shouldRedirect,
    redirectTo 
  })
}

// Hook to check if a route should be protected
export const useRouteProtection = () => {
  const pathname = usePathname()
  
  const isPublicRoute = PUBLIC_ROUTES.some(route => 
    pathname === route || pathname.startsWith(route + '/')
  )
  
  const isAuthRoute = AUTH_REDIRECT_ROUTES.includes(pathname)
  const isDashboardRoute = pathname.startsWith('/dashboard')
  const isApiRoute = pathname.startsWith('/api')
  
  return {
    isPublicRoute,
    isAuthRoute,
    isDashboardRoute,
    isApiRoute,
    requiresAuth: !isPublicRoute && !isApiRoute,
  }
}
