/**
 * Authentication Testing Utilities
 * 
 * This file contains utilities to test the authentication flow
 * in development and staging environments.
 */

import { createSupabaseClient } from '@/lib/supabase'

export interface AuthTestResult {
  test: string
  passed: boolean
  message: string
  details?: any
}

export class AuthTester {
  private supabase = createSupabaseClient()
  private results: AuthTestResult[] = []

  // Test user registration
  async testSignUp(email: string, password: string): Promise<AuthTestResult> {
    try {
      const { data, error } = await this.supabase.auth.signUp({
        email,
        password
      })

      if (error) {
        return {
          test: 'Sign Up',
          passed: false,
          message: error.message,
          details: error
        }
      }

      return {
        test: 'Sign Up',
        passed: true,
        message: 'User registration successful',
        details: { userId: data.user?.id, needsConfirmation: !data.user?.email_confirmed_at }
      }
    } catch (error: any) {
      return {
        test: 'Sign Up',
        passed: false,
        message: error.message || 'Sign up failed',
        details: error
      }
    }
  }

  // Test user login
  async testSignIn(email: string, password: string): Promise<AuthTestResult> {
    try {
      const { data, error } = await this.supabase.auth.signInWithPassword({
        email,
        password
      })

      if (error) {
        return {
          test: 'Sign In',
          passed: false,
          message: error.message,
          details: error
        }
      }

      return {
        test: 'Sign In',
        passed: true,
        message: 'User login successful',
        details: { 
          userId: data.user?.id, 
          sessionId: data.session?.access_token?.substring(0, 10) + '...',
          expiresAt: data.session?.expires_at
        }
      }
    } catch (error: any) {
      return {
        test: 'Sign In',
        passed: false,
        message: error.message || 'Sign in failed',
        details: error
      }
    }
  }

  // Test session retrieval
  async testGetSession(): Promise<AuthTestResult> {
    try {
      const { data, error } = await this.supabase.auth.getSession()

      if (error) {
        return {
          test: 'Get Session',
          passed: false,
          message: error.message,
          details: error
        }
      }

      const hasSession = !!data.session
      return {
        test: 'Get Session',
        passed: true,
        message: hasSession ? 'Active session found' : 'No active session',
        details: { 
          hasSession,
          userId: data.session?.user?.id,
          expiresAt: data.session?.expires_at
        }
      }
    } catch (error: any) {
      return {
        test: 'Get Session',
        passed: false,
        message: error.message || 'Session retrieval failed',
        details: error
      }
    }
  }

  // Test API endpoint with authentication
  async testAuthenticatedAPI(endpoint: string): Promise<AuthTestResult> {
    try {
      const { data: { session } } = await this.supabase.auth.getSession()

      if (!session) {
        return {
          test: `API ${endpoint}`,
          passed: false,
          message: 'No active session for API test',
          details: null
        }
      }

      const response = await fetch(endpoint, {
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json',
        },
      })

      const responseData = await response.json()

      return {
        test: `API ${endpoint}`,
        passed: response.ok,
        message: response.ok ? 'API call successful' : `API call failed: ${response.status}`,
        details: {
          status: response.status,
          data: responseData
        }
      }
    } catch (error: any) {
      return {
        test: `API ${endpoint}`,
        passed: false,
        message: error.message || 'API test failed',
        details: error
      }
    }
  }

  // Test sign out
  async testSignOut(): Promise<AuthTestResult> {
    try {
      const { error } = await this.supabase.auth.signOut()

      if (error) {
        return {
          test: 'Sign Out',
          passed: false,
          message: error.message,
          details: error
        }
      }

      return {
        test: 'Sign Out',
        passed: true,
        message: 'User signed out successfully',
        details: null
      }
    } catch (error: any) {
      return {
        test: 'Sign Out',
        passed: false,
        message: error.message || 'Sign out failed',
        details: error
      }
    }
  }

  // Run comprehensive authentication tests
  async runFullAuthTest(testEmail: string, testPassword: string): Promise<AuthTestResult[]> {
    const results: AuthTestResult[] = []

    console.log('🧪 Starting comprehensive authentication tests...')

    // Test 1: Sign up
    console.log('1. Testing sign up...')
    const signUpResult = await this.testSignUp(testEmail, testPassword)
    results.push(signUpResult)

    // Test 2: Sign in
    console.log('2. Testing sign in...')
    const signInResult = await this.testSignIn(testEmail, testPassword)
    results.push(signInResult)

    // Test 3: Get session
    console.log('3. Testing session retrieval...')
    const sessionResult = await this.testGetSession()
    results.push(sessionResult)

    // Test 4: Test authenticated API endpoints
    console.log('4. Testing authenticated API endpoints...')
    const apiEndpoints = [
      '/api/user/profile',
      '/api/user/sites',
      '/api/stripe/billing-data'
    ]

    for (const endpoint of apiEndpoints) {
      const apiResult = await this.testAuthenticatedAPI(endpoint)
      results.push(apiResult)
    }

    // Test 5: Sign out
    console.log('5. Testing sign out...')
    const signOutResult = await this.testSignOut()
    results.push(signOutResult)

    // Test 6: Verify session is cleared
    console.log('6. Verifying session is cleared...')
    const postSignOutSession = await this.testGetSession()
    results.push({
      ...postSignOutSession,
      test: 'Post-SignOut Session Check',
      passed: !postSignOutSession.details?.hasSession
    })

    return results
  }

  // Generate test report
  generateReport(results: AuthTestResult[]): string {
    const passed = results.filter(r => r.passed).length
    const total = results.length
    const passRate = ((passed / total) * 100).toFixed(1)

    let report = `
🔐 Authentication Test Report
============================

Summary: ${passed}/${total} tests passed (${passRate}%)

Test Results:
`

    results.forEach((result, index) => {
      const status = result.passed ? '✅' : '❌'
      report += `${index + 1}. ${status} ${result.test}: ${result.message}\n`
      
      if (result.details && !result.passed) {
        report += `   Details: ${JSON.stringify(result.details, null, 2)}\n`
      }
    })

    report += `
Recommendations:
${passed === total ? '🎉 All tests passed! Authentication is working correctly.' : '⚠️  Some tests failed. Please review the failed tests and fix the issues.'}
`

    return report
  }
}

// Utility function to run tests in development
export async function runAuthTests(testEmail?: string, testPassword?: string) {
  if (process.env.NODE_ENV !== 'development') {
    console.warn('Authentication tests should only be run in development environment')
    return
  }

  const email = testEmail || `test-${Date.now()}@example.com`
  const password = testPassword || 'testpassword123'

  const tester = new AuthTester()
  const results = await tester.runFullAuthTest(email, password)
  const report = tester.generateReport(results)

  console.log(report)
  return { results, report }
}
