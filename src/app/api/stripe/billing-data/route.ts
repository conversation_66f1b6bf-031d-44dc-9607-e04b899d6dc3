import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { getAuthenticatedUser, getUserProfile } from '@/lib/auth-utils';

// Get the authenticated user's Stripe Customer ID
async function getStripeCustomerId(req: NextRequest): Promise<{ customerId: string | null; error: string | null }> {
  try {
    // Get authenticated user
    const { user, error: authError } = await getAuthenticatedUser(req);

    if (!user) {
      return { customerId: null, error: authError || 'Authentication required' };
    }

    // Get user profile with Stripe customer ID
    const { profile, error: profileError } = await getUserProfile(user.id);

    if (!profile) {
      return { customerId: null, error: profileError || 'User profile not found' };
    }

    return { customerId: profile.stripe_customer_id, error: null };
  } catch (error: any) {
    return { customerId: null, error: error.message || 'Failed to get customer ID' };
  }
}

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-06-30.basil',
});

export async function GET(req: NextRequest) {
  try {
    const { customerId, error } = await getStripeCustomerId(req);

    if (!customerId) {
      return NextResponse.json({ error: error || 'User not authenticated.' }, { status: 401 });
    }

    // Fetch subscriptions, invoices, and payment methods from Stripe
    const [subscriptions, invoices, paymentMethods] = await Promise.all([
      stripe.subscriptions.list({ customer: customerId, limit: 1 }),
      stripe.invoices.list({ customer: customerId, limit: 10 }),
      stripe.paymentMethods.list({ customer: customerId, type: 'card' }),
    ]);

    // Format the data to send to the frontend
    const responseData = {
      subscription: subscriptions.data[0] ? {
        status: subscriptions.data[0].status,
        plan: subscriptions.data[0].items.data[0]?.price.nickname || 'N/A',
        amount: subscriptions.data[0].items.data[0]?.price.unit_amount / 100,
        currency: subscriptions.data[0].items.data[0]?.price.currency,
        nextBillingDate: new Date(subscriptions.data[0].current_period_end * 1000).toISOString(),
      } : null,
      invoices: invoices.data.map(invoice => ({
        id: invoice.id,
        date: new Date(invoice.created * 1000).toISOString(),
        amount: invoice.amount_paid / 100,
        status: invoice.status,
        url: invoice.invoice_pdf,
      })),
      paymentMethods: paymentMethods.data.map(pm => ({
        id: pm.id,
        type: pm.type,
        last4: pm.card?.last4,
        brand: pm.card?.brand,
        isDefault: false, // Stripe API doesn't directly flag a default PM in this list view
      })),
    };

    return NextResponse.json(responseData);

  } catch (error: any) {
    console.error('Stripe API Error:', error.message);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
