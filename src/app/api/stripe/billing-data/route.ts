import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';

// This is a placeholder for your actual user session logic.
// In a real app, you would get the authenticated user's Stripe Customer ID.
async function getStripeCustomerId(req: NextRequest): Promise<string | null> {
  // Example: Replace with your actual authentication logic (e.g., from a session cookie, JWT, etc.)
  // For demonstration, we'll use a hardcoded customer ID.
  // In a real scenario, you might have something like:
  // const session = await getSession(req);
  // return session?.user?.stripeCustomerId;
  return 'cus_SdM8oip3kppTF3'; // Replace with a valid Stripe Customer ID from your test data
}

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2023-10-16',
});

export async function GET(req: NextRequest) {
  try {
    const customerId = await getStripeCustomerId(req);

    if (!customerId) {
      return NextResponse.json({ error: 'User not authenticated.' }, { status: 401 });
    }

    // Fetch subscriptions, invoices, and payment methods from Stripe
    const [subscriptions, invoices, paymentMethods] = await Promise.all([
      stripe.subscriptions.list({ customer: customerId, limit: 1 }),
      stripe.invoices.list({ customer: customerId, limit: 10 }),
      stripe.paymentMethods.list({ customer: customerId, type: 'card' }),
    ]);

    // Format the data to send to the frontend
    const responseData = {
      subscription: subscriptions.data[0] ? {
        status: subscriptions.data[0].status,
        plan: subscriptions.data[0].items.data[0]?.price.nickname || 'N/A',
        amount: subscriptions.data[0].items.data[0]?.price.unit_amount / 100,
        currency: subscriptions.data[0].items.data[0]?.price.currency,
        nextBillingDate: new Date(subscriptions.data[0].current_period_end * 1000).toISOString(),
      } : null,
      invoices: invoices.data.map(invoice => ({
        id: invoice.id,
        date: new Date(invoice.created * 1000).toISOString(),
        amount: invoice.amount_paid / 100,
        status: invoice.status,
        url: invoice.invoice_pdf,
      })),
      paymentMethods: paymentMethods.data.map(pm => ({
        id: pm.id,
        type: pm.type,
        last4: pm.card?.last4,
        brand: pm.card?.brand,
        isDefault: false, // Stripe API doesn't directly flag a default PM in this list view
      })),
    };

    return NextResponse.json(responseData);

  } catch (error: any) {
    console.error('Stripe API Error:', error.message);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
