import { NextRequest } from 'next/server'
import { createAuthenticatedRoute, createSuccessResponse, createErrorResponse } from '@/lib/api-auth'
import { getUserSites, createUserSite, updateUserSite, deleteUserSite } from '@/lib/auth-utils'

// GET /api/user/sites - Get all sites for the authenticated user
export const GET = createAuthenticatedRoute({
  allowedMethods: ['GET'],
  handler: async (req: NextRequest, user: any) => {
    try {
      const { sites, error } = await getUserSites(user.id)
      
      if (error) {
        return createErrorResponse(error, 500)
      }

      return createSuccessResponse({ sites })
    } catch (error: any) {
      console.error('Error fetching user sites:', error)
      return createErrorResponse('Failed to fetch sites', 500)
    }
  }
})

// POST /api/user/sites - Create a new site for the authenticated user
export const POST = createAuthenticatedRoute({
  allowedMethods: ['POST'],
  requiredFields: ['site_name'],
  handler: async (req: NextRequest, user: any) => {
    try {
      const body = (req as any).parsedBody
      const { site_name, expiry_status, expiry_time } = body

      const { site, error } = await createUserSite(user.id, {
        site_name,
        expiry_status: expiry_status || 'Temporary',
        expiry_time: expiry_time || null
      })

      if (error) {
        return createErrorResponse(error, 500)
      }

      return createSuccessResponse({ site }, 201)
    } catch (error: any) {
      console.error('Error creating user site:', error)
      return createErrorResponse('Failed to create site', 500)
    }
  }
})

// PUT /api/user/sites - Update a site for the authenticated user
export const PUT = createAuthenticatedRoute({
  allowedMethods: ['PUT'],
  requiredFields: ['site_id'],
  handler: async (req: NextRequest, user: any) => {
    try {
      const body = (req as any).parsedBody
      const { site_id, site_name, expiry_status, expiry_time } = body

      const updates: any = {}
      if (site_name !== undefined) updates.site_name = site_name
      if (expiry_status !== undefined) updates.expiry_status = expiry_status
      if (expiry_time !== undefined) updates.expiry_time = expiry_time

      const { site, error } = await updateUserSite(user.id, site_id, updates)

      if (error) {
        return createErrorResponse(error, 500)
      }

      return createSuccessResponse({ site })
    } catch (error: any) {
      console.error('Error updating user site:', error)
      return createErrorResponse('Failed to update site', 500)
    }
  }
})

// DELETE /api/user/sites - Delete a site for the authenticated user
export const DELETE = createAuthenticatedRoute({
  allowedMethods: ['DELETE'],
  requiredFields: ['site_id'],
  handler: async (req: NextRequest, user: any) => {
    try {
      const body = (req as any).parsedBody
      const { site_id } = body

      const { success, error } = await deleteUserSite(user.id, site_id)

      if (error) {
        return createErrorResponse(error, 500)
      }

      return createSuccessResponse({ success })
    } catch (error: any) {
      console.error('Error deleting user site:', error)
      return createErrorResponse('Failed to delete site', 500)
    }
  }
})
