import { NextRequest } from 'next/server'
import { createAuthenticatedRoute, createSuccessResponse, createErrorResponse } from '@/lib/api-auth'
import { getUserProfile, upsertUserProfile } from '@/lib/auth-utils'

// GET /api/user/profile - Get the authenticated user's profile
export const GET = createAuthenticatedRoute({
  allowedMethods: ['GET'],
  handler: async (req: NextRequest, user: any) => {
    try {
      const { profile, error } = await getUserProfile(user.id)
      
      if (error) {
        return createErrorResponse(error, 500)
      }

      // If no profile exists, create one with basic user info
      if (!profile) {
        const { profile: newProfile, error: createError } = await upsertUserProfile(user.id, {
          email: user.email,
          full_name: user.user_metadata?.full_name || null,
          avatar_url: user.user_metadata?.avatar_url || null
        })

        if (createError) {
          return createErrorResponse(createError, 500)
        }

        return createSuccessResponse({ profile: newProfile })
      }

      return createSuccessResponse({ profile })
    } catch (error: any) {
      console.error('Error fetching user profile:', error)
      return createErrorResponse('Failed to fetch profile', 500)
    }
  }
})

// PUT /api/user/profile - Update the authenticated user's profile
export const PUT = createAuthenticatedRoute({
  allowedMethods: ['PUT'],
  handler: async (req: NextRequest, user: any) => {
    try {
      const body = (req as any).parsedBody
      const { full_name, avatar_url, stripe_customer_id } = body

      const updates: any = {
        email: user.email // Always keep email in sync
      }
      
      if (full_name !== undefined) updates.full_name = full_name
      if (avatar_url !== undefined) updates.avatar_url = avatar_url
      if (stripe_customer_id !== undefined) updates.stripe_customer_id = stripe_customer_id

      const { profile, error } = await upsertUserProfile(user.id, updates)

      if (error) {
        return createErrorResponse(error, 500)
      }

      return createSuccessResponse({ profile })
    } catch (error: any) {
      console.error('Error updating user profile:', error)
      return createErrorResponse('Failed to update profile', 500)
    }
  }
})
