import { NextRequest, NextResponse } from 'next/server';

interface NamecheapDomainResult {
  Domain: string;
  Available: boolean;
  IsPremiumName: boolean;
  PremiumRegistrationPrice?: number;
  PremiumRenewalPrice?: number;
  PremiumRestorePrice?: number;
  PremiumTransferPrice?: number;
  IcannFee?: number;
  EapFee?: number;
}

interface DomainCheckResult {
  Domain: string;
  Available: boolean;
  IsPremiumName: boolean;
  Price?: number;
  Error?: string;
}

// Common TLDs with their approximate pricing (in USD, will be converted to AUD)
const COMMON_TLDS = [
  { tld: 'com', price: 12.98 },
  { tld: 'net', price: 14.98 },
  { tld: 'org', price: 14.98 },
  { tld: 'info', price: 18.98 },
  { tld: 'biz', price: 18.98 },
  { tld: 'com.au', price: 16.50 },
  { tld: 'net.au', price: 16.50 },
  { tld: 'org.au', price: 16.50 },
  { tld: 'co', price: 32.98 },
  { tld: 'io', price: 59.98 }
];

// USD to AUD conversion rate (approximate)
const USD_TO_AUD_RATE = 1.5;

async function checkDomainAvailability(domainList: string[]): Promise<NamecheapDomainResult[]> {
  const apiUser = process.env.NAMECHEAP_API_USER;
  const apiKey = process.env.NAMECHEAP_API_KEY;
  const username = process.env.NAMECHEAP_USERNAME;
  const clientIp = process.env.NAMECHEAP_CLIENT_IP;
  const sandbox = process.env.NAMECHEAP_SANDBOX === 'true';

  if (!apiUser || !apiKey || !username || !clientIp) {
    throw new Error('Missing Namecheap API configuration');
  }

  const baseUrl = sandbox 
    ? 'https://api.sandbox.namecheap.com/xml.response'
    : 'https://api.namecheap.com/xml.response';

  const params = new URLSearchParams({
    ApiUser: apiUser,
    ApiKey: apiKey,
    UserName: username,
    Command: 'namecheap.domains.check',
    ClientIp: clientIp,
    DomainList: domainList.join(',')
  });

  try {
    const response = await fetch(`${baseUrl}?${params.toString()}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const xmlText = await response.text();

    // Simple XML parsing for domain check results
    const domainResults: NamecheapDomainResult[] = [];

    // Check for API errors first
    const errorMatch = xmlText.match(/<Error[^>]*>([^<]*)<\/Error>/);
    if (errorMatch) {
      throw new Error(`Namecheap API error: ${errorMatch[1]}`);
    }

    // Parse domain check results using regex
    const domainRegex = /<DomainCheckResult[^>]*Domain="([^"]*)"[^>]*Available="([^"]*)"[^>]*IsPremiumName="([^"]*)"[^>]*(?:PremiumRegistrationPrice="([^"]*)")?[^>]*\/>/g;

    let match;
    while ((match = domainRegex.exec(xmlText)) !== null) {
      const [, domainName, available, isPremium, premiumPrice] = match;

      const result: NamecheapDomainResult = {
        Domain: domainName,
        Available: available === 'true',
        IsPremiumName: isPremium === 'true',
      };

      if (result.IsPremiumName && premiumPrice) {
        result.PremiumRegistrationPrice = parseFloat(premiumPrice);
      }

      domainResults.push(result);
    }

    return domainResults;
  } catch (error) {
    console.error('Namecheap API error:', error);
    throw error;
  }
}

function generateDomainVariations(baseDomain: string): string[] {
  // Remove any existing TLD
  const domainName = baseDomain.replace(/\.(com|net|org|info|biz|com\.au|net\.au|org\.au|co|io)$/i, '');
  
  // Generate variations with common TLDs
  return COMMON_TLDS.map(({ tld }) => `${domainName}.${tld}`);
}

function getPriceForDomain(domain: string, namecheapResult: NamecheapDomainResult): number {
  // If it's a premium domain, use the premium price
  if (namecheapResult.IsPremiumName && namecheapResult.PremiumRegistrationPrice) {
    return namecheapResult.PremiumRegistrationPrice * USD_TO_AUD_RATE + 5; // Add 5 AUD markup
  }

  // Otherwise, use our standard pricing
  const tld = domain.split('.').slice(1).join('.');
  const tldInfo = COMMON_TLDS.find(t => t.tld === tld);
  
  if (tldInfo) {
    return tldInfo.price * USD_TO_AUD_RATE + 5; // Add 5 AUD markup
  }

  // Default price if TLD not found
  return 20 * USD_TO_AUD_RATE + 5;
}

export async function POST(request: NextRequest) {
  try {
    const { domain, action } = await request.json();

    if (!domain) {
      return NextResponse.json({ error: 'Domain is required' }, { status: 400 });
    }

    if (action === 'check') {
      // Generate domain variations
      const domainVariations = generateDomainVariations(domain);
      
      // Check availability with Namecheap
      const namecheapResults = await checkDomainAvailability(domainVariations);
      
      // Format results for frontend
      const results: DomainCheckResult[] = namecheapResults.map(result => {
        const formattedResult: DomainCheckResult = {
          Domain: result.Domain,
          Available: result.Available,
          IsPremiumName: result.IsPremiumName,
        };

        // Add price if available
        if (result.Available) {
          formattedResult.Price = getPriceForDomain(result.Domain, result);
        }

        return formattedResult;
      });

      return NextResponse.json({ results });
    }

    if (action === 'register') {
      // For now, return a mock success response
      // In a real implementation, you would call the Namecheap domain registration API
      return NextResponse.json({ 
        success: true, 
        message: `Domain ${domain} registration initiated`,
        orderId: `mock-order-${Date.now()}`
      });
    }

    return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    );
  }
}
