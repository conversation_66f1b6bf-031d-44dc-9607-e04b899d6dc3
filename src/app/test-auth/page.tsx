'use client'

import { useState } from 'react'
import { runAuthTests, AuthTestResult } from '@/utils/auth-test'
import { useAuth } from '@/context/AuthContext'
import { ErrorDisplay, SuccessDisplay, Loading } from '@/components/ErrorBoundary'

export default function AuthTestPage() {
  const [testResults, setTestResults] = useState<AuthTestResult[]>([])
  const [testReport, setTestReport] = useState<string>('')
  const [isRunning, setIsRunning] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [testEmail, setTestEmail] = useState('')
  const [testPassword, setTestPassword] = useState('')
  
  const { user, session } = useAuth()

  const handleRunTests = async () => {
    if (process.env.NODE_ENV !== 'development') {
      setError('Authentication tests can only be run in development environment')
      return
    }

    setIsRunning(true)
    setError(null)
    setTestResults([])
    setTestReport('')

    try {
      const email = testEmail || `test-${Date.now()}@example.com`
      const password = testPassword || 'testpassword123'
      
      const result = await runAuthTests(email, password)
      
      if (result) {
        setTestResults(result.results)
        setTestReport(result.report)
      }
    } catch (err: any) {
      setError(err.message || 'Failed to run authentication tests')
    } finally {
      setIsRunning(false)
    }
  }

  const getStatusIcon = (passed: boolean) => {
    return passed ? '✅' : '❌'
  }

  const getStatusColor = (passed: boolean) => {
    return passed ? 'text-green-600' : 'text-red-600'
  }

  if (process.env.NODE_ENV !== 'development') {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Authentication Tests</h1>
          <p className="text-gray-600">
            Authentication tests are only available in development environment.
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h1 className="text-2xl font-bold text-gray-900">Authentication Test Suite</h1>
            <p className="mt-1 text-sm text-gray-600">
              Test the authentication flow and API endpoints
            </p>
          </div>

          <div className="p-6">
            {/* Current Auth Status */}
            <div className="mb-6 p-4 bg-blue-50 rounded-lg">
              <h2 className="text-lg font-medium text-blue-900 mb-2">Current Authentication Status</h2>
              <div className="space-y-2 text-sm">
                <p><strong>User:</strong> {user ? user.email : 'Not authenticated'}</p>
                <p><strong>Session:</strong> {session ? 'Active' : 'None'}</p>
                <p><strong>User ID:</strong> {user?.id || 'N/A'}</p>
              </div>
            </div>

            {/* Test Configuration */}
            <div className="mb-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4">Test Configuration</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Test Email (optional)
                  </label>
                  <input
                    type="email"
                    value={testEmail}
                    onChange={(e) => setTestEmail(e.target.value)}
                    placeholder="<EMAIL>"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Test Password (optional)
                  </label>
                  <input
                    type="password"
                    value={testPassword}
                    onChange={(e) => setTestPassword(e.target.value)}
                    placeholder="testpassword123"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>
              <p className="mt-2 text-xs text-gray-500">
                Leave empty to use auto-generated test credentials
              </p>
            </div>

            {/* Error Display */}
            <ErrorDisplay error={error} className="mb-4" onDismiss={() => setError(null)} />

            {/* Run Tests Button */}
            <div className="mb-6">
              <button
                onClick={handleRunTests}
                disabled={isRunning}
                className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isRunning ? 'Running Tests...' : 'Run Authentication Tests'}
              </button>
            </div>

            {/* Loading */}
            {isRunning && <Loading message="Running authentication tests..." className="mb-6" />}

            {/* Test Results */}
            {testResults.length > 0 && (
              <div className="mb-6">
                <h2 className="text-lg font-medium text-gray-900 mb-4">Test Results</h2>
                <div className="space-y-3">
                  {testResults.map((result, index) => (
                    <div
                      key={index}
                      className={`p-4 rounded-lg border ${
                        result.passed ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <span className="text-lg">{getStatusIcon(result.passed)}</span>
                          <span className="font-medium">{result.test}</span>
                        </div>
                        <span className={`text-sm ${getStatusColor(result.passed)}`}>
                          {result.passed ? 'PASSED' : 'FAILED'}
                        </span>
                      </div>
                      <p className="mt-1 text-sm text-gray-600">{result.message}</p>
                      {result.details && !result.passed && (
                        <details className="mt-2">
                          <summary className="cursor-pointer text-xs text-gray-500">
                            Show details
                          </summary>
                          <pre className="mt-1 text-xs bg-gray-100 p-2 rounded overflow-auto">
                            {JSON.stringify(result.details, null, 2)}
                          </pre>
                        </details>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Test Report */}
            {testReport && (
              <div className="mb-6">
                <h2 className="text-lg font-medium text-gray-900 mb-4">Test Report</h2>
                <pre className="bg-gray-100 p-4 rounded-lg text-sm overflow-auto whitespace-pre-wrap">
                  {testReport}
                </pre>
              </div>
            )}

            {/* Manual Test Links */}
            <div className="border-t pt-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4">Manual Testing</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <a
                  href="/login"
                  className="block p-4 border border-gray-200 rounded-lg hover:bg-gray-50"
                >
                  <h3 className="font-medium">Login Page</h3>
                  <p className="text-sm text-gray-600">Test user authentication</p>
                </a>
                <a
                  href="/signup"
                  className="block p-4 border border-gray-200 rounded-lg hover:bg-gray-50"
                >
                  <h3 className="font-medium">Signup Page</h3>
                  <p className="text-sm text-gray-600">Test user registration</p>
                </a>
                <a
                  href="/dashboard"
                  className="block p-4 border border-gray-200 rounded-lg hover:bg-gray-50"
                >
                  <h3 className="font-medium">Dashboard</h3>
                  <p className="text-sm text-gray-600">Test protected route</p>
                </a>
                <a
                  href="/dashboard/billing"
                  className="block p-4 border border-gray-200 rounded-lg hover:bg-gray-50"
                >
                  <h3 className="font-medium">Billing</h3>
                  <p className="text-sm text-gray-600">Test billing integration</p>
                </a>
                <a
                  href="/dashboard/account"
                  className="block p-4 border border-gray-200 rounded-lg hover:bg-gray-50"
                >
                  <h3 className="font-medium">Account</h3>
                  <p className="text-sm text-gray-600">Test profile management</p>
                </a>
                <a
                  href="/api/user/profile"
                  target="_blank"
                  className="block p-4 border border-gray-200 rounded-lg hover:bg-gray-50"
                >
                  <h3 className="font-medium">API Test</h3>
                  <p className="text-sm text-gray-600">Test API authentication</p>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
