'use client';

import { useState, useEffect } from 'react';
import { useRequireAuth } from '@/hooks/useAuthGuard';
import { createSupabaseClient } from '@/lib/supabase';

// Mock data for UI development (fallback)
const mockData = {
  subscription: {
    status: 'active',
    plan: 'Pro Plan',
    amount: 99.99,
    currency: 'usd',
    nextBillingDate: '2023-12-01',
  },
  invoices: [
    { id: 'in_1', date: '2023-11-01', amount: 99.99, status: 'paid', url: '#' },
    { id: 'in_2', date: '2023-10-01', amount: 99.99, status: 'paid', url: '#' },
  ],
  paymentMethods: [
    { id: 'pm_1', type: 'card', last4: '4242', brand: 'Visa', isDefault: true },
    { id: 'pm_2', type: 'card', last4: '1234', brand: 'Mastercard', isDefault: false },
  ],
};

export default function BillingPage() {
  const [activeTab, setActiveTab] = useState('subscription');
  const [billingData, setBillingData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Ensure user is authenticated
  const { user } = useRequireAuth();
  const supabase = createSupabaseClient();

  useEffect(() => {
    async function fetchBillingData() {
      if (!user) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);

        // Get the session to include in API requests
        const { data: { session } } = await supabase.auth.getSession();

        if (!session) {
          throw new Error('No active session');
        }

        const response = await fetch('/api/stripe/billing-data', {
          headers: {
            'Authorization': `Bearer ${session.access_token}`,
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error('Failed to fetch billing data.');
        }

        const data = await response.json();
        setBillingData(data);
      } catch (err: any) {
        console.error('Billing data fetch error:', err);
        setError(err.message);
        // Fallback to mock data on error for UI development
        setBillingData(mockData);
      } finally {
        setLoading(false);
      }
    }

    fetchBillingData();
  }, [user, supabase]);

  const renderContent = () => {
    if (loading) {
      return <div>Loading...</div>;
    }

    if (error && !billingData) {
      return <div className="text-red-500">Error: {error}</div>;
    }

    if (!billingData) {
      return <div>No billing data found.</div>;
    }

    switch (activeTab) {
      case 'subscription':
        if (!billingData.subscription) {
          return <div>No active subscription found.</div>;
        }

        return (
          <div>
            <h2 className="text-xl font-semibold mb-4">Current Subscription</h2>
            <p>Status: <span className="capitalize font-medium text-green-600">{billingData.subscription.status}</span></p>
            <p>Plan: {billingData.subscription.plan}</p>
            <p>Next Billing Date: {new Date(billingData.subscription.nextBillingDate).toLocaleDateString()}</p>
          </div>
        );
      case 'invoices':
        return (
          <div>
            <h2 className="text-xl font-semibold mb-4">Billing History</h2>
            <ul>
              {billingData.invoices.map((invoice: any) => (
                <li key={invoice.id} className="border-b py-2 flex justify-between">
                  <span>{new Date(invoice.date).toLocaleDateString()} - ${invoice.amount.toFixed(2)}</span>
                  <a href={invoice.url} target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline">View Invoice</a>
                </li>
              ))}
            </ul>
          </div>
        );
      case 'paymentMethods':
        return (
          <div>
            <h2 className="text-xl font-semibold mb-4">Payment Methods</h2>
            <ul>
              {billingData.paymentMethods.map((pm: any) => (
                <li key={pm.id} className="border-b py-2">
                  <span>{pm.brand} ending in {pm.last4} {pm.isDefault && '(Default)'}</span>
                </li>
              ))}
            </ul>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="p-6 bg-white rounded-lg shadow-md">
      <h1 className="text-2xl font-bold mb-6">Billing</h1>
      <div className="border-b mb-6">
        <nav className="-mb-px flex space-x-8">
          <button onClick={() => setActiveTab('subscription')} className={`whitespace-nowrap pb-4 px-1 border-b-2 font-medium text-sm ${activeTab === 'subscription' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}>
            Subscription
          </button>
          <button onClick={() => setActiveTab('invoices')} className={`whitespace-nowrap pb-4 px-1 border-b-2 font-medium text-sm ${activeTab === 'invoices' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}>
            Invoices
          </button>
          <button onClick={() => setActiveTab('paymentMethods')} className={`whitespace-nowrap pb-4 px-1 border-b-2 font-medium text-sm ${activeTab === 'paymentMethods' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}>
            Payment Methods
          </button>
        </nav>
      </div>
      <div>
        {renderContent()}
      </div>
    </div>
  );
}
