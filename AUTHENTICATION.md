# WordPress AI App - Authentication Implementation

This document describes the comprehensive authentication and authorization system implemented using Supabase Auth.

## Overview

The authentication system provides:
- User registration and login with email/password
- OAuth authentication (Google, Apple, Microsoft)
- Route protection and middleware
- API endpoint security
- User profile management
- Session management
- Error handling and user feedback

## Architecture

### Core Components

1. **Authentication Context** (`src/context/AuthContext.tsx`)
   - Manages global auth state
   - Provides auth methods (signIn, signUp, signOut)
   - Handles session management

2. **Route Protection** (`src/hooks/useAuthGuard.ts`)
   - Custom hooks for route protection
   - Automatic redirects for unauthenticated users
   - Public route definitions

3. **Middleware** (`middleware.ts`)
   - Server-side route protection
   - Automatic redirects based on auth status
   - Session refresh handling

4. **API Security** (`src/lib/api-auth.ts`)
   - JWT token validation
   - Authenticated API route wrapper
   - User authorization helpers

5. **Error Handling** (`src/hooks/useErrorHandler.ts`)
   - Comprehensive error management
   - User-friendly error messages
   - Automatic retry mechanisms

## File Structure

```
src/
├── context/
│   └── AuthContext.tsx          # Global auth state management
├── hooks/
│   ├── useAuthGuard.ts          # Route protection hooks
│   └── useErrorHandler.ts       # Error handling utilities
├── lib/
│   ├── supabase.ts             # Supabase client configuration
│   ├── auth-utils.ts           # Server-side auth utilities
│   └── api-auth.ts             # API authentication helpers
├── components/
│   ├── ErrorBoundary.tsx       # Error boundary and display components
│   └── UserProfile.tsx         # User profile components
├── app/
│   ├── login/page.tsx          # Login page
│   ├── signup/page.tsx         # Signup page
│   ├── dashboard/              # Protected dashboard routes
│   ├── api/user/               # User-specific API endpoints
│   └── test-auth/page.tsx      # Authentication testing page
└── utils/
    └── auth-test.ts            # Authentication testing utilities
```

## Protected Routes

The following routes require authentication:
- `/dashboard/*` - All dashboard pages
- `/account/*` - Account management
- `/billing/*` - Billing and subscription management
- `/themes/*` - Theme customization
- `/content-editor/*` - Content editing
- `/domain-setup/*` - Domain configuration

## API Endpoints

### User Management
- `GET /api/user/profile` - Get user profile
- `PUT /api/user/profile` - Update user profile
- `GET /api/user/sites` - Get user's sites
- `POST /api/user/sites` - Create new site
- `PUT /api/user/sites` - Update site
- `DELETE /api/user/sites` - Delete site

### Billing
- `GET /api/stripe/billing-data` - Get user's billing information

All API endpoints require a valid JWT token in the Authorization header:
```
Authorization: Bearer <jwt_token>
```

## Usage Examples

### Using Authentication Context

```tsx
import { useAuth } from '@/context/AuthContext'

function MyComponent() {
  const { user, signIn, signOut, isLoading, error } = useAuth()

  const handleLogin = async () => {
    const { error } = await signIn(email, password)
    if (!error) {
      // Login successful
    }
  }

  return (
    <div>
      {user ? (
        <p>Welcome, {user.email}!</p>
      ) : (
        <button onClick={handleLogin}>Login</button>
      )}
    </div>
  )
}
```

### Protecting Routes

```tsx
import { useRequireAuth } from '@/hooks/useAuthGuard'

function ProtectedPage() {
  const { user, isLoading } = useRequireAuth()

  if (isLoading) return <div>Loading...</div>

  return (
    <div>
      <h1>Protected Content</h1>
      <p>User: {user.email}</p>
    </div>
  )
}
```

### Creating Authenticated API Routes

```tsx
import { createAuthenticatedRoute } from '@/lib/api-auth'

export const GET = createAuthenticatedRoute({
  allowedMethods: ['GET'],
  handler: async (req, user) => {
    // user is guaranteed to be authenticated
    const data = await getUserData(user.id)
    return NextResponse.json({ data })
  }
})
```

### Error Handling

```tsx
import { useErrorHandler } from '@/hooks/useErrorHandler'

function MyComponent() {
  const { withErrorHandling, error, success } = useErrorHandler()

  const handleOperation = () => {
    withErrorHandling(
      async () => {
        // Your async operation
        await someApiCall()
      },
      {
        successMessage: 'Operation completed successfully!',
        errorMessage: 'Failed to complete operation'
      }
    )
  }

  return (
    <div>
      {error && <ErrorDisplay error={error.message} />}
      {success && <SuccessDisplay message={success.message} />}
      <button onClick={handleOperation}>Perform Operation</button>
    </div>
  )
}
```

## Environment Variables

Required environment variables:

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

## Database Schema

The authentication system expects the following Supabase tables:

### profiles
```sql
CREATE TABLE profiles (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  email TEXT NOT NULL,
  full_name TEXT,
  avatar_url TEXT,
  stripe_customer_id TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### sites
```sql
CREATE TABLE sites (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) NOT NULL,
  site_name TEXT NOT NULL,
  expiry_status TEXT DEFAULT 'Temporary',
  expiry_time TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## Testing

### Automated Testing

Run the authentication test suite in development:

```tsx
import { runAuthTests } from '@/utils/auth-test'

// Run with default test credentials
await runAuthTests()

// Run with custom credentials
await runAuthTests('<EMAIL>', 'password123')
```

### Manual Testing

Visit `/test-auth` in development to access the authentication test interface.

### Test Scenarios

1. **User Registration**
   - Valid email/password
   - Invalid email format
   - Weak password
   - Duplicate email

2. **User Login**
   - Valid credentials
   - Invalid credentials
   - Unverified email

3. **Route Protection**
   - Access protected routes without auth
   - Access protected routes with auth
   - Automatic redirects

4. **API Security**
   - API calls without token
   - API calls with invalid token
   - API calls with valid token

5. **Session Management**
   - Session persistence
   - Session expiration
   - Logout functionality

## Security Considerations

1. **JWT Tokens**: All API requests require valid JWT tokens
2. **Route Protection**: Middleware protects all sensitive routes
3. **User Isolation**: All user data is filtered by authenticated user ID
4. **Error Handling**: Sensitive information is not exposed in error messages
5. **Session Management**: Automatic session refresh and cleanup

## Troubleshooting

### Common Issues

1. **"No authorization token provided"**
   - Ensure the client is sending the Authorization header
   - Check that the user is properly authenticated

2. **"Authentication required" on protected routes**
   - Verify middleware configuration
   - Check that the user session is valid

3. **API calls failing with 401**
   - Ensure the JWT token is being sent correctly
   - Verify the token hasn't expired

### Debug Mode

In development, enable debug logging by setting:
```env
NODE_ENV=development
```

This will show detailed error information and stack traces.

## Future Enhancements

- Two-factor authentication
- Social login providers
- Role-based access control
- Audit logging
- Advanced session management
- Password reset functionality
